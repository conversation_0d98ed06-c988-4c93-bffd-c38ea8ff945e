-- Fix workspace permissions and add missing functions/tables
-- Run this in your Supabase SQL editor

-- 1. First, check if the workspace tables exist
DO $$
BEGIN
    -- Check if workspaces table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'workspaces') THEN
        RAISE EXCEPTION 'Workspaces table does not exist. Please run migration 0008_workspaces.sql first';
    END IF;
END $$;

-- 2. Create the create_workspace function if it doesn't exist
CREATE OR REPLACE FUNCTION create_workspace(p_name text, p_user_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_workspace_id uuid;
  v_result json;
BEGIN
  -- Validate inputs
  IF p_name IS NULL OR trim(p_name) = '' THEN
    RAISE EXCEPTION 'Workspace name is required';
  END IF;
  
  -- Create the workspace
  INSERT INTO workspaces (name)
  VALUES (p_name)
  RETURNING id INTO v_workspace_id;
  
  -- Add the user as admin
  INSERT INTO workspace_users (workspace_id, user_id, role)
  VALUES (v_workspace_id, p_user_id, 'admin');
  
  -- Return the created workspace
  SELECT json_build_object(
    'id', id,
    'name', name,
    'created_at', created_at
  ) INTO v_result
  FROM workspaces
  WHERE id = v_workspace_id;
  
  RETURN v_result;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Failed to create workspace: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_workspace(text, uuid) TO authenticated;

-- 3. Fix workspace insert policy
DROP POLICY IF EXISTS "workspace_admin_write" ON workspaces;
DROP POLICY IF EXISTS "workspace_admin_insert" ON workspaces;
DROP POLICY IF EXISTS "workspace_insert" ON workspaces;

-- Allow authenticated users to create workspaces
CREATE POLICY "workspace_insert" ON workspaces
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Allow admins to update/delete their workspaces
CREATE POLICY "workspace_admin_update" ON workspaces
FOR UPDATE
USING (EXISTS (
  SELECT 1 FROM workspace_users 
  WHERE workspace_id = workspaces.id 
  AND user_id = auth.uid() 
  AND role = 'admin'
));

CREATE POLICY "workspace_admin_delete" ON workspaces
FOR DELETE
USING (EXISTS (
  SELECT 1 FROM workspace_users 
  WHERE workspace_id = workspaces.id 
  AND user_id = auth.uid() 
  AND role = 'admin'
));

-- 4. Create workspace_invitations table
CREATE TABLE IF NOT EXISTS workspace_invitations (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  workspace_id uuid REFERENCES workspaces ON DELETE CASCADE NOT NULL,
  email text NOT NULL,
  role text NOT NULL CHECK (role IN ('admin', 'viewer')),
  token uuid DEFAULT uuid_generate_v4() NOT NULL,
  invited_by uuid REFERENCES auth.users NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  expires_at timestamptz DEFAULT (now() + interval '7 days') NOT NULL,
  accepted_at timestamptz,
  UNIQUE(workspace_id, email)
);

-- Enable RLS on workspace_invitations
ALTER TABLE workspace_invitations ENABLE ROW LEVEL SECURITY;

-- Policies for workspace_invitations
CREATE POLICY "workspace_invitations_admin_all" ON workspace_invitations
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM workspace_users
    WHERE workspace_id = workspace_invitations.workspace_id
    AND user_id = auth.uid()
    AND role = 'admin'
  )
);

-- Allow users to view invitations sent to their email
CREATE POLICY "workspace_invitations_recipient_read" ON workspace_invitations
FOR SELECT
USING (
  email = (SELECT email FROM auth.users WHERE id = auth.uid())
);

-- 5. Create function to accept invitation
CREATE OR REPLACE FUNCTION accept_workspace_invitation(p_token uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_invitation record;
  v_user_email text;
  v_result json;
BEGIN
  -- Get user email
  SELECT email INTO v_user_email
  FROM auth.users
  WHERE id = auth.uid();
  
  -- Find valid invitation
  SELECT * INTO v_invitation
  FROM workspace_invitations
  WHERE token = p_token
  AND email = v_user_email
  AND accepted_at IS NULL
  AND expires_at > now();
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or expired invitation';
  END IF;
  
  -- Add user to workspace
  INSERT INTO workspace_users (workspace_id, user_id, role)
  VALUES (v_invitation.workspace_id, auth.uid(), v_invitation.role)
  ON CONFLICT (workspace_id, user_id) DO UPDATE
  SET role = EXCLUDED.role;
  
  -- Mark invitation as accepted
  UPDATE workspace_invitations
  SET accepted_at = now()
  WHERE id = v_invitation.id;
  
  -- Return workspace info
  SELECT json_build_object(
    'workspace_id', w.id,
    'workspace_name', w.name,
    'role', v_invitation.role
  ) INTO v_result
  FROM workspaces w
  WHERE w.id = v_invitation.workspace_id;
  
  RETURN v_result;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION accept_workspace_invitation(uuid) TO authenticated;

-- 6. Verify your user has a workspace
DO $$
DECLARE
  v_user_id uuid;
  v_workspace_count int;
BEGIN
  -- Get current user id (you'll need to replace this with your actual user ID)
  -- You can find your user ID in Supabase dashboard under Authentication > Users
  v_user_id := auth.uid();
  
  -- Count user's workspaces
  SELECT COUNT(*) INTO v_workspace_count
  FROM workspace_users
  WHERE user_id = v_user_id;
  
  IF v_workspace_count = 0 THEN
    RAISE NOTICE 'Current user has no workspaces. Creating a default workspace...';
    
    -- Create a default workspace for the user
    PERFORM create_workspace('My Workspace', v_user_id);
  ELSE
    RAISE NOTICE 'User has % workspace(s)', v_workspace_count;
  END IF;
END $$;

-- 7. List all workspaces for debugging
SELECT 
  w.id,
  w.name,
  wu.user_id,
  wu.role,
  u.email
FROM workspaces w
JOIN workspace_users wu ON w.id = wu.workspace_id
LEFT JOIN auth.users u ON wu.user_id = u.id
ORDER BY w.created_at DESC; 