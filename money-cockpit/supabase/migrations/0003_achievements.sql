-- Create achievements table
CREATE TABLE IF NOT EXISTS public.achievements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_id TEXT NOT NULL,
    unlocked_at TIMESTAMPTZ DEFAULT NOW(),
    points INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, achievement_id)
);

-- Create user_stats table for gamification
CREATE TABLE IF NOT EXISTS public.user_stats (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    total_points INTEGER DEFAULT 0,
    current_level INTEGER DEFAULT 1,
    transactions_count INTEGER DEFAULT 0,
    debts_paid_count INTEGER DEFAULT 0,
    savings_streak_days INTEGER DEFAULT 0,
    last_transaction_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- RLS policies for achievements
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own achievements" ON public.achievements
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert achievements" ON public.achievements
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS policies for user_stats
ALTER TABLE public.user_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own stats" ON public.user_stats
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own stats" ON public.user_stats
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert user stats" ON public.user_stats
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to check and award achievements
CREATE OR REPLACE FUNCTION check_achievements()
RETURNS TRIGGER AS $$
DECLARE
    v_user_stats RECORD;
    v_achievement_exists BOOLEAN;
BEGIN
    -- Get user stats
    SELECT * INTO v_user_stats FROM public.user_stats WHERE user_id = NEW.user_id;
    
    -- Create user stats if not exists
    IF NOT FOUND THEN
        INSERT INTO public.user_stats (user_id) VALUES (NEW.user_id);
        SELECT * INTO v_user_stats FROM public.user_stats WHERE user_id = NEW.user_id;
    END IF;
    
    -- Check for first transaction achievement
    IF v_user_stats.transactions_count = 0 THEN
        INSERT INTO public.achievements (user_id, achievement_id, points)
        VALUES (NEW.user_id, 'first_transaction', 100)
        ON CONFLICT (user_id, achievement_id) DO NOTHING;
    END IF;
    
    -- Update transaction count
    UPDATE public.user_stats 
    SET transactions_count = transactions_count + 1,
        last_transaction_date = CURRENT_DATE,
        updated_at = NOW()
    WHERE user_id = NEW.user_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for transaction achievements
CREATE TRIGGER check_transaction_achievements
AFTER INSERT ON public.transactions
FOR EACH ROW
EXECUTE FUNCTION check_achievements();

-- Function to check debt payoff achievements
CREATE OR REPLACE FUNCTION check_debt_payoff()
RETURNS TRIGGER AS $$
BEGIN
    -- If debt is fully paid (balance = 0)
    IF NEW.current_balance = 0 AND OLD.current_balance > 0 THEN
        -- Award debt destroyer achievement
        INSERT INTO public.achievements (user_id, achievement_id, points)
        VALUES (NEW.user_id, 'debt_destroyer', 500)
        ON CONFLICT (user_id, achievement_id) DO NOTHING;
        
        -- Update debt paid count
        UPDATE public.user_stats 
        SET debts_paid_count = debts_paid_count + 1,
            updated_at = NOW()
        WHERE user_id = NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for debt achievements
CREATE TRIGGER check_debt_achievements
AFTER UPDATE ON public.debts
FOR EACH ROW
EXECUTE FUNCTION check_debt_payoff();

-- Function to get user achievements
CREATE OR REPLACE FUNCTION get_user_achievements(p_user_id UUID)
RETURNS TABLE (
    achievement_id TEXT,
    unlocked_at TIMESTAMPTZ,
    points INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT a.achievement_id, a.unlocked_at, a.points
    FROM public.achievements a
    WHERE a.user_id = p_user_id
    ORDER BY a.unlocked_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user level and stats
CREATE OR REPLACE FUNCTION get_user_level_stats(p_user_id UUID)
RETURNS TABLE (
    total_points INTEGER,
    current_level INTEGER,
    transactions_count INTEGER,
    debts_paid_count INTEGER,
    savings_streak_days INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(us.total_points, 0),
        COALESCE(us.current_level, 1),
        COALESCE(us.transactions_count, 0),
        COALESCE(us.debts_paid_count, 0),
        COALESCE(us.savings_streak_days, 0)
    FROM public.user_stats us
    WHERE us.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 