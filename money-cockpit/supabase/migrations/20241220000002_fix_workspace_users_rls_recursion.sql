-- Drop existing problematic policies that cause infinite recursion
DROP POLICY IF EXISTS "workspace_users_read" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_insert" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_update" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_delete" ON workspace_users;

-- Create new policies that avoid recursion by using workspaces table as intermediary
-- Allow users to read workspace_users if they are a member of that workspace
CREATE POLICY "workspace_users_read_policy" ON workspace_users
FOR SELECT USING (
  user_id = auth.uid() OR 
  workspace_id IN (
    SELECT w.id FROM workspaces w 
    JOIN workspace_users wu ON w.id = wu.workspace_id 
    WHERE wu.user_id = auth.uid()
  )
);

-- Allow workspace admins to insert new members
CREATE POLICY "workspace_users_admin_insert_policy" ON workspace_users
FOR INSERT WITH CHECK (
  workspace_id IN (
    SELECT w.id FROM workspaces w 
    JOIN workspace_users wu ON w.id = wu.workspace_id 
    WHERE wu.user_id = auth.uid() AND wu.role = 'admin'
  )
);

-- Allow workspace admins to update members
CREATE POLICY "workspace_users_admin_update_policy" ON workspace_users
FOR UPDATE USING (
  workspace_id IN (
    SELECT w.id FROM workspaces w 
    JOIN workspace_users wu ON w.id = wu.workspace_id 
    WHERE wu.user_id = auth.uid() AND wu.role = 'admin'
  )
);

-- Allow workspace admins to delete members
CREATE POLICY "workspace_users_admin_delete_policy" ON workspace_users
FOR DELETE USING (
  workspace_id IN (
    SELECT w.id FROM workspaces w 
    JOIN workspace_users wu ON w.id = wu.workspace_id 
    WHERE wu.user_id = auth.uid() AND wu.role = 'admin'
  )
); 