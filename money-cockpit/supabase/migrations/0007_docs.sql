-- Create invoices_uploaded table
CREATE TABLE IF NOT EXISTS invoices_uploaded (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  entity_id UUID REFERENCES entities(id),
  storage_path TEXT NOT NULL,
  status TEXT DEFAULT 'UNPAID' CHECK (status IN ('UNPAID', 'PAID', 'PARTIAL', 'ERROR')),
  amount NUMERIC,
  currency CHAR(3) DEFAULT 'AUD',
  due_date DATE,
  category TEXT,
  vendor TEXT,
  paid_txn_id UUID REFERENCES transactions(id),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE invoices_uploaded ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view own invoices" ON invoices_uploaded
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own invoices" ON invoices_uploaded
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own invoices" ON invoices_uploaded
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own invoices" ON invoices_uploaded
    FOR DELETE USING (auth.uid() = user_id);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_invoices_uploaded_entity_id ON invoices_uploaded(entity_id);
CREATE INDEX IF NOT EXISTS idx_invoices_uploaded_status ON invoices_uploaded(status);
CREATE INDEX IF NOT EXISTS idx_invoices_uploaded_due_date ON invoices_uploaded(due_date);

-- Create storage bucket for documents (this will be done via Supabase dashboard)
-- INSERT INTO storage.buckets (id, name, public) VALUES ('docs', 'docs', false); 