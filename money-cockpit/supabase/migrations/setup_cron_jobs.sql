-- Setup cron jobs for MoneyMofos Edge Functions
-- Run this in Supabase Dashboard > SQL Editor

-- Enable pg_cron extension if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Clear any existing cron jobs (optional - only if you need to reset)
-- SELECT cron.unschedule('interest-accrual');
-- SELECT cron.unschedule('recurring-expenses');
-- SELECT cron.unschedule('budget-snapshots');
-- SELECT cron.unschedule('cfo-digest');
-- SELECT cron.unschedule('missed-payment-checker');
-- SELECT cron.unschedule('invoice-matcher');

-- 1. Interest accrual - runs nightly at 2 AM AEST (4 PM UTC)
SELECT cron.schedule(
  'interest-accrual',
  '0 16 * * *',
  $$
  SELECT net.http_post(
    url := 'https://qolgcdsvmaajybhsdnmg.supabase.co/functions/v1/interest_accrual',
    headers := '{"Authorization": "Bearer ' || current_setting('app.settings.service_role_key') || '"}'::jsonb
  );
  $$
);

-- 2. Recurring expenses - runs daily at 3 AM AEST (5 PM UTC)
SELECT cron.schedule(
  'recurring-expenses',
  '0 17 * * *',
  $$
  SELECT net.http_post(
    url := 'https://qolgcdsvmaajybhsdnmg.supabase.co/functions/v1/recurring_expenses_post',
    headers := '{"Authorization": "Bearer ' || current_setting('app.settings.service_role_key') || '"}'::jsonb
  );
  $$
);

-- 3. Budget snapshots - runs daily at 4 AM AEST (6 PM UTC)
SELECT cron.schedule(
  'budget-snapshots',
  '0 18 * * *',
  $$
  SELECT net.http_post(
    url := 'https://qolgcdsvmaajybhsdnmg.supabase.co/functions/v1/budget_snapshot',
    headers := '{"Authorization": "Bearer ' || current_setting('app.settings.service_role_key') || '"}'::jsonb
  );
  $$
);

-- 4. CFO Digest - runs daily at 5 AM AEST (7 PM UTC)
SELECT cron.schedule(
  'cfo-digest',
  '0 19 * * *',
  $$
  SELECT net.http_post(
    url := 'https://qolgcdsvmaajybhsdnmg.supabase.co/functions/v1/cfo_digest',
    headers := '{"Authorization": "Bearer ' || current_setting('app.settings.service_role_key') || '"}'::jsonb
  );
  $$
);

-- 5. Missed payment checker - runs daily at 6 AM AEST (8 PM UTC)
SELECT cron.schedule(
  'missed-payment-checker',
  '0 20 * * *',
  $$
  SELECT net.http_post(
    url := 'https://qolgcdsvmaajybhsdnmg.supabase.co/functions/v1/missed_payment_checker',
    headers := '{"Authorization": "Bearer ' || current_setting('app.settings.service_role_key') || '"}'::jsonb
  );
  $$
);

-- 6. Invoice matcher - runs hourly
SELECT cron.schedule(
  'invoice-matcher',
  '0 * * * *',
  $$
  SELECT net.http_post(
    url := 'https://qolgcdsvmaajybhsdnmg.supabase.co/functions/v1/invoice_matcher',
    headers := '{"Authorization": "Bearer ' || current_setting('app.settings.service_role_key') || '"}'::jsonb
  );
  $$
);

-- View all scheduled jobs
SELECT * FROM cron.job; 