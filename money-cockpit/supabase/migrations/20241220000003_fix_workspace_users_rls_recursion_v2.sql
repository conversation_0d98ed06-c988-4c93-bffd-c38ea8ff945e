-- Disable <PERSON><PERSON> temporarily to avoid recursion during policy changes
ALTER TABLE workspace_users DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "workspace_users_read_policy" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_insert_policy" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_update_policy" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_delete_policy" ON workspace_users;

-- Create a function to check workspace membership without recursion
CREATE OR REPLACE FUNCTION is_workspace_member(workspace_id uuid, user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM workspace_users wu
    WHERE wu.workspace_id = $1 AND wu.user_id = $2
  );
$$;

-- Create a function to check workspace admin status without recursion
CREATE OR REPLACE FUNCTION is_workspace_admin(workspace_id uuid, user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM workspace_users wu
    WHERE wu.workspace_id = $1 AND wu.user_id = $2 AND wu.role = 'admin'
  );
$$;

-- Create new policies using functions to avoid recursion
CREATE POLICY "workspace_users_select_policy" ON workspace_users
FOR SELECT USING (
  user_id = auth.uid() OR 
  is_workspace_member(workspace_id, auth.uid())
);

CREATE POLICY "workspace_users_insert_policy" ON workspace_users
FOR INSERT WITH CHECK (
  is_workspace_admin(workspace_id, auth.uid())
);

CREATE POLICY "workspace_users_update_policy" ON workspace_users
FOR UPDATE USING (
  is_workspace_admin(workspace_id, auth.uid())
);

CREATE POLICY "workspace_users_delete_policy" ON workspace_users
FOR DELETE USING (
  is_workspace_admin(workspace_id, auth.uid())
);

-- Re-enable RLS
ALTER TABLE workspace_users ENABLE ROW LEVEL SECURITY; 