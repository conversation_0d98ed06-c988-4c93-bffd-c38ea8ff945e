-- Fix workspace creation RLS policies and add helper function

-- Drop the existing problematic insert policy
DROP POLICY IF EXISTS "workspace_admin_insert" ON workspaces;

-- Create a new insert policy that allows any authenticated user to create a workspace
CREATE POLICY "workspace_insert" ON workspaces
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Create a function to handle workspace creation
CREATE OR REPLACE FUNCTION create_workspace(p_name text, p_user_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_workspace_id uuid;
  v_result json;
BEGIN
  -- Validate inputs
  IF p_name IS NULL OR trim(p_name) = '' THEN
    RAISE EXCEPTION 'Workspace name is required';
  END IF;
  
  -- Create the workspace
  INSERT INTO workspaces (name)
  VALUES (p_name)
  RETURNING id INTO v_workspace_id;
  
  -- Add the user as admin
  INSERT INTO workspace_users (workspace_id, user_id, role)
  VALUES (v_workspace_id, p_user_id, 'admin');
  
  -- Return the created workspace
  SELECT json_build_object(
    'id', id,
    'name', name,
    'created_at', created_at
  ) INTO v_result
  FROM workspaces
  WHERE id = v_workspace_id;
  
  RETURN v_result;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Failed to create workspace: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_workspace(text, uuid) TO authenticated; 