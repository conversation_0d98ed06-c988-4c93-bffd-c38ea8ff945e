-- Add missing columns to entities table
ALTER TABLE entities 
ADD COLUMN IF NOT EXISTS default_currency CHAR(3) DEFAULT 'AUD',
ADD COLUMN IF NOT EXISTS is_gst BOOLEAN DEFAULT false;

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    scope TEXT NOT NULL CHECK (scope IN ('personal', 'business', 'both')),
    gst_default BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user preferences table
CREATE TABLE IF NOT EXISTS user_prefs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    pay_day TEXT DEFAULT 'Friday',
    date_fmt TEXT DEFAULT 'd MMM',
    default_lens TEXT DEFAULT 'personal',
    last_viewed_lens TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Seed categories
INSERT INTO categories (name, scope, gst_default) VALUES
    ('Food & Dining', 'both', false),
    ('Fuel', 'both', true),
    ('Utilities', 'both', true),
    ('Software', 'business', true),
    ('Rent', 'both', true),
    ('Payroll', 'business', false),
    ('Transport', 'both', true),
    ('Entertainment', 'personal', false),
    ('Health', 'both', false),
    ('Education', 'both', false)
ON CONFLICT (name) DO NOTHING;

-- Enable RLS on new tables
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_prefs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for categories (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view categories" ON categories
    FOR SELECT USING (auth.uid() IS NOT NULL);

-- RLS Policies for user_prefs
CREATE POLICY "Users can view own preferences" ON user_prefs
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own preferences" ON user_prefs
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own preferences" ON user_prefs
    FOR UPDATE USING (auth.uid() = user_id);

-- Create RPC functions for entity management
CREATE OR REPLACE FUNCTION add_entity(
    p_name TEXT,
    p_type TEXT,
    p_abn_acn TEXT DEFAULT NULL,
    p_default_currency CHAR(3) DEFAULT 'AUD',
    p_is_gst BOOLEAN DEFAULT false
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_entity_id UUID;
    v_user_id UUID;
BEGIN
    v_user_id := auth.uid();
    
    -- Validate ABN if provided
    IF p_abn_acn IS NOT NULL AND p_type = 'business' THEN
        IF NOT (p_abn_acn ~ '^\d{11}$' OR p_abn_acn ~ '^\d{9}$') THEN
            RAISE EXCEPTION 'Invalid ABN/ACN format';
        END IF;
        
        -- Check for duplicate ABN
        IF EXISTS (SELECT 1 FROM entities WHERE abn_acn = p_abn_acn) THEN
            RAISE EXCEPTION 'ABN/ACN already exists';
        END IF;
    END IF;
    
    -- Insert entity
    INSERT INTO entities (owner_id, name, type, abn_acn, default_currency, is_gst)
    VALUES (v_user_id, p_name, p_type, p_abn_acn, p_default_currency, p_is_gst)
    RETURNING id INTO v_entity_id;
    
    RETURN v_entity_id;
END;
$$;

CREATE OR REPLACE FUNCTION update_entity(
    p_entity_id UUID,
    p_name TEXT,
    p_abn_acn TEXT DEFAULT NULL,
    p_default_currency CHAR(3) DEFAULT 'AUD',
    p_is_gst BOOLEAN DEFAULT false,
    p_is_active BOOLEAN DEFAULT true
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
    v_entity_type TEXT;
BEGIN
    v_user_id := auth.uid();
    
    -- Check ownership
    SELECT type INTO v_entity_type
    FROM entities 
    WHERE id = p_entity_id AND owner_id = v_user_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Access denied or entity not found';
    END IF;
    
    -- Validate ABN if provided
    IF p_abn_acn IS NOT NULL AND v_entity_type = 'business' THEN
        IF NOT (p_abn_acn ~ '^\d{11}$' OR p_abn_acn ~ '^\d{9}$') THEN
            RAISE EXCEPTION 'Invalid ABN/ACN format';
        END IF;
        
        -- Check for duplicate ABN (excluding current entity)
        IF EXISTS (SELECT 1 FROM entities WHERE abn_acn = p_abn_acn AND id != p_entity_id) THEN
            RAISE EXCEPTION 'ABN/ACN already exists';
        END IF;
    END IF;
    
    -- Update entity
    UPDATE entities
    SET 
        name = p_name,
        abn_acn = p_abn_acn,
        default_currency = p_default_currency,
        is_gst = p_is_gst,
        is_active = p_is_active
    WHERE id = p_entity_id AND owner_id = v_user_id;
    
    RETURN true;
END;
$$;

CREATE OR REPLACE FUNCTION update_user_prefs(
    p_pay_day TEXT DEFAULT NULL,
    p_date_fmt TEXT DEFAULT NULL,
    p_default_lens TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
BEGIN
    v_user_id := auth.uid();
    
    -- Insert or update preferences
    INSERT INTO user_prefs (user_id, pay_day, date_fmt, default_lens)
    VALUES (v_user_id, 
        COALESCE(p_pay_day, 'Friday'),
        COALESCE(p_date_fmt, 'd MMM'),
        COALESCE(p_default_lens, 'personal')
    )
    ON CONFLICT (user_id) DO UPDATE
    SET 
        pay_day = COALESCE(p_pay_day, user_prefs.pay_day),
        date_fmt = COALESCE(p_date_fmt, user_prefs.date_fmt),
        default_lens = COALESCE(p_default_lens, user_prefs.default_lens),
        updated_at = NOW();
    
    RETURN true;
END;
$$; 