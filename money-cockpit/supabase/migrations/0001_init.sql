-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Reference tables
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('personal', 'business', 'trust')),
    abn_acn TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    provider TEXT NOT NULL,
    currency CHAR(3) DEFAULT 'AUD',
    display_name TEXT NOT NULL,
    card_wallet_tag TEXT,
    api_key TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE income_streams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    counterparty TEXT NOT NULL,
    stream_type TEXT NOT NULL,
    ownership_pct NUMERIC NOT NULL,
    currency CHAR(3) DEFAULT 'AUD',
    data_source TEXT,
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    amount NUMERIC NOT NULL,
    currency CHAR(3) DEFAULT 'AUD',
    date DATE NOT NULL,
    category TEXT,
    service_type TEXT,
    personal_category TEXT,
    bucket TEXT,
    memo TEXT,
    stream_id UUID REFERENCES income_streams(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE debts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    creditor TEXT NOT NULL,
    original_amount NUMERIC NOT NULL,
    balance NUMERIC NOT NULL,
    interest_rate NUMERIC NOT NULL,
    compounding TEXT NOT NULL,
    payment_freq TEXT NOT NULL,
    next_due DATE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE recurring_expenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    exp_type TEXT NOT NULL,
    frequency TEXT NOT NULL,
    amount NUMERIC NOT NULL,
    currency CHAR(3) DEFAULT 'AUD',
    next_due DATE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE goals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    target_amount NUMERIC,
    target_date DATE,
    status TEXT DEFAULT 'active',
    icon TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    stripe_id TEXT UNIQUE,
    client TEXT NOT NULL,
    amount NUMERIC NOT NULL,
    tax_amount NUMERIC DEFAULT 0,
    currency CHAR(3) DEFAULT 'AUD',
    status TEXT NOT NULL,
    due_date DATE NOT NULL,
    paid_date DATE,
    pdf_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE tax_calendar (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    tax_type TEXT NOT NULL,
    due_date DATE NOT NULL,
    est_amount NUMERIC,
    status TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE buckets (
    name TEXT PRIMARY KEY,
    target_pct NUMERIC NOT NULL CHECK (target_pct >= 0 AND target_pct <= 100)
);

-- Insert default buckets
INSERT INTO buckets (name, target_pct) VALUES
    ('Daily', 25),
    ('Splurge', 25),
    ('Smile', 25),
    ('Fire', 25);

CREATE TABLE category_rules (
    id SERIAL PRIMARY KEY,
    category TEXT NOT NULL,
    match_regex TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE agent_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lens TEXT NOT NULL,
    headline TEXT NOT NULL,
    body TEXT NOT NULL,
    severity TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_entities_owner_id ON entities(owner_id);
CREATE INDEX idx_accounts_entity_id ON accounts(entity_id);
CREATE INDEX idx_transactions_account_id ON transactions(account_id);
CREATE INDEX idx_transactions_date ON transactions(date);
CREATE INDEX idx_transactions_category ON transactions(category);
CREATE INDEX idx_debts_entity_id ON debts(entity_id);
CREATE INDEX idx_recurring_expenses_entity_id ON recurring_expenses(entity_id);
CREATE INDEX idx_recurring_expenses_next_due ON recurring_expenses(next_due);
CREATE INDEX idx_goals_owner_id ON goals(owner_id);
CREATE INDEX idx_invoices_entity_id ON invoices(entity_id);
CREATE INDEX idx_invoices_stripe_id ON invoices(stripe_id);
CREATE INDEX idx_tax_calendar_entity_id ON tax_calendar(entity_id);
CREATE INDEX idx_agent_insights_lens ON agent_insights(lens);
CREATE INDEX idx_agent_insights_created_at ON agent_insights(created_at);

-- Enable Row Level Security
ALTER TABLE entities ENABLE ROW LEVEL SECURITY;
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE recurring_expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE income_streams ENABLE ROW LEVEL SECURITY;
ALTER TABLE goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE tax_calendar ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_insights ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Entities: Users can only see their own entities
CREATE POLICY "Users can view own entities" ON entities
    FOR SELECT USING (auth.uid() = owner_id);
CREATE POLICY "Users can insert own entities" ON entities
    FOR INSERT WITH CHECK (auth.uid() = owner_id);
CREATE POLICY "Users can update own entities" ON entities
    FOR UPDATE USING (auth.uid() = owner_id);
CREATE POLICY "Users can delete own entities" ON entities
    FOR DELETE USING (auth.uid() = owner_id);

-- Accounts: Users can manage accounts for their entities
CREATE POLICY "Users can view accounts of own entities" ON accounts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM entities 
            WHERE entities.id = accounts.entity_id 
            AND entities.owner_id = auth.uid()
        )
    );
CREATE POLICY "Users can insert accounts for own entities" ON accounts
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM entities 
            WHERE entities.id = accounts.entity_id 
            AND entities.owner_id = auth.uid()
        )
    );
CREATE POLICY "Users can update accounts of own entities" ON accounts
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM entities 
            WHERE entities.id = accounts.entity_id 
            AND entities.owner_id = auth.uid()
        )
    );
CREATE POLICY "Users can delete accounts of own entities" ON accounts
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM entities 
            WHERE entities.id = accounts.entity_id 
            AND entities.owner_id = auth.uid()
        )
    );

-- Transactions: Users can manage transactions for their accounts
CREATE POLICY "Users can view transactions of own accounts" ON transactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM accounts 
            JOIN entities ON entities.id = accounts.entity_id
            WHERE accounts.id = transactions.account_id 
            AND entities.owner_id = auth.uid()
        )
    );
CREATE POLICY "Users can insert transactions for own accounts" ON transactions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM accounts 
            JOIN entities ON entities.id = accounts.entity_id
            WHERE accounts.id = transactions.account_id 
            AND entities.owner_id = auth.uid()
        )
    );
CREATE POLICY "Users can update transactions of own accounts" ON transactions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM accounts 
            JOIN entities ON entities.id = accounts.entity_id
            WHERE accounts.id = transactions.account_id 
            AND entities.owner_id = auth.uid()
        )
    );
CREATE POLICY "Users can delete transactions of own accounts" ON transactions
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM accounts 
            JOIN entities ON entities.id = accounts.entity_id
            WHERE accounts.id = transactions.account_id 
            AND entities.owner_id = auth.uid()
        )
    );

-- Similar policies for other tables
CREATE POLICY "Users can manage debts of own entities" ON debts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM entities 
            WHERE entities.id = debts.entity_id 
            AND entities.owner_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage recurring expenses of own entities" ON recurring_expenses
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM entities 
            WHERE entities.id = recurring_expenses.entity_id 
            AND entities.owner_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage income streams of own entities" ON income_streams
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM entities 
            WHERE entities.id = income_streams.entity_id 
            AND entities.owner_id = auth.uid()
        )
    );

CREATE POLICY "Users can view own goals" ON goals
    FOR SELECT USING (auth.uid() = owner_id);
CREATE POLICY "Users can insert own goals" ON goals
    FOR INSERT WITH CHECK (auth.uid() = owner_id);
CREATE POLICY "Users can update own goals" ON goals
    FOR UPDATE USING (auth.uid() = owner_id);
CREATE POLICY "Users can delete own goals" ON goals
    FOR DELETE USING (auth.uid() = owner_id);

CREATE POLICY "Users can manage invoices of own entities" ON invoices
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM entities 
            WHERE entities.id = invoices.entity_id 
            AND entities.owner_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage tax calendar of own entities" ON tax_calendar
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM entities 
            WHERE entities.id = tax_calendar.entity_id 
            AND entities.owner_id = auth.uid()
        )
    );

-- Agent insights are viewable by all authenticated users (filtered by lens in app)
CREATE POLICY "Authenticated users can view insights" ON agent_insights
    FOR SELECT USING (auth.uid() IS NOT NULL);

-- Create RPC functions
CREATE OR REPLACE FUNCTION get_dashboard(p_lens TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    user_id UUID;
BEGIN
    user_id := auth.uid();
    
    WITH filtered_entities AS (
        SELECT id FROM entities 
        WHERE owner_id = user_id 
        AND is_active = true
        AND (
            p_lens = 'all' OR 
            (p_lens = 'personal' AND type = 'personal') OR
            (p_lens != 'personal' AND p_lens != 'all' AND name = p_lens)
        )
    ),
    account_balances AS (
        SELECT 
            a.entity_id,
            SUM(t.amount) as balance,
            t.currency
        FROM accounts a
        JOIN transactions t ON t.account_id = a.id
        WHERE a.entity_id IN (SELECT id FROM filtered_entities)
        GROUP BY a.entity_id, t.currency
    ),
    debt_totals AS (
        SELECT 
            SUM(balance) as total_debt
        FROM debts
        WHERE entity_id IN (SELECT id FROM filtered_entities)
    ),
    upcoming_payments AS (
        SELECT 
            SUM(amount) as total_upcoming
        FROM recurring_expenses
        WHERE entity_id IN (SELECT id FROM filtered_entities)
        AND next_due <= CURRENT_DATE + INTERVAL '14 days'
    )
    SELECT json_build_object(
        'net_worth', COALESCE((SELECT SUM(balance) FROM account_balances), 0) - COALESCE((SELECT total_debt FROM debt_totals), 0),
        'total_assets', COALESCE((SELECT SUM(balance) FROM account_balances), 0),
        'total_debt', COALESCE((SELECT total_debt FROM debt_totals), 0),
        'upcoming_payments', COALESCE((SELECT total_upcoming FROM upcoming_payments), 0),
        'lens', p_lens
    ) INTO result;
    
    RETURN result;
END;
$$;

CREATE OR REPLACE FUNCTION get_latest_insight(p_lens TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'id', id,
        'headline', headline,
        'body', body,
        'severity', severity,
        'created_at', created_at
    ) INTO result
    FROM agent_insights
    WHERE lens = p_lens
    ORDER BY created_at DESC
    LIMIT 1;
    
    RETURN result;
END;
$$;

CREATE OR REPLACE FUNCTION debt_payoff_forecast()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    user_id UUID;
BEGIN
    user_id := auth.uid();
    
    -- Simplified debt payoff calculation
    -- In production, this would implement avalanche vs snowball algorithms
    WITH user_debts AS (
        SELECT 
            d.id,
            d.creditor,
            d.balance,
            d.interest_rate,
            d.payment_freq,
            e.name as entity_name
        FROM debts d
        JOIN entities e ON e.id = d.entity_id
        WHERE e.owner_id = user_id
        ORDER BY d.interest_rate DESC -- Avalanche method
    )
    SELECT json_build_object(
        'avalanche', json_agg(
            json_build_object(
                'creditor', creditor,
                'balance', balance,
                'interest_rate', interest_rate,
                'entity', entity_name
            ) ORDER BY interest_rate DESC
        ),
        'snowball', json_agg(
            json_build_object(
                'creditor', creditor,
                'balance', balance,
                'interest_rate', interest_rate,
                'entity', entity_name
            ) ORDER BY balance ASC
        )
    ) INTO result
    FROM user_debts;
    
    RETURN result;
END;
$$; 