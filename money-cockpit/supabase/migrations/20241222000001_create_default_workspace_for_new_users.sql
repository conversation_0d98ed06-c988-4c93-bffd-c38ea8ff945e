-- Update the handle_new_user function to create a default workspace for new users
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  new_workspace_id uuid;
BEGIN
  -- Create profile for the new user
  INSERT INTO public.profiles (id, email)
  VALUES (new.id, new.email)
  ON CONFLICT (id) DO NOTHING;
  
  -- Create a default workspace for the new user
  INSERT INTO public.workspaces (name)
  VALUES (COALESCE(new.raw_user_meta_data->>'full_name', split_part(new.email, '@', 1)) || '''s Workspace')
  RETURNING id INTO new_workspace_id;
  
  -- Add the user as admin of their workspace
  INSERT INTO public.workspace_users (workspace_id, user_id, role)
  VALUES (new_workspace_id, new.id, 'admin');
  
  -- Create default categories for the user (categories are user-specific, not workspace-specific)
  INSERT INTO public.categories (name, scope, user_id)
  VALUES 
    ('Food & Dining', 'personal', new.id),
    ('Transportation', 'personal', new.id),
    ('Shopping', 'personal', new.id),
    ('Entertainment', 'personal', new.id),
    ('Bills & Utilities', 'personal', new.id),
    ('Healthcare', 'personal', new.id),
    ('Education', 'personal', new.id),
    ('Travel', 'personal', new.id),
    ('Personal Care', 'personal', new.id),
    ('Gifts & Donations', 'personal', new.id),
    ('Office Supplies', 'business', new.id),
    ('Marketing', 'business', new.id),
    ('Payroll', 'business', new.id),
    ('Software & Tools', 'business', new.id),
    ('Professional Services', 'business', new.id),
    ('Travel & Expenses', 'business', new.id),
    ('Equipment', 'business', new.id),
    ('Insurance', 'both', new.id),
    ('Taxes', 'both', new.id),
    ('Other', 'both', new.id)
  ON CONFLICT (name) DO NOTHING;
  
  -- Create default buckets for personal use with target percentages
  INSERT INTO public.buckets (name, workspace_id, target_pct)
  VALUES 
    ('Emergency Fund', new_workspace_id, 20),
    ('Vacation', new_workspace_id, 10),
    ('Home Improvement', new_workspace_id, 10),
    ('Car Maintenance', new_workspace_id, 5),
    ('Christmas', new_workspace_id, 5),
    ('Medical', new_workspace_id, 10),
    ('Clothing', new_workspace_id, 5),
    ('Fun Money', new_workspace_id, 10);
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate the trigger to ensure it uses the updated function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- For existing users who don't have a workspace, create one
DO $$
DECLARE
  user_record record;
  new_workspace_id uuid;
BEGIN
  -- Find users without workspaces
  FOR user_record IN 
    SELECT u.id, u.email, u.raw_user_meta_data
    FROM auth.users u
    WHERE NOT EXISTS (
      SELECT 1 FROM workspace_users wu WHERE wu.user_id = u.id
    )
  LOOP
    -- Create workspace for this user
    INSERT INTO workspaces (name)
    VALUES (COALESCE(user_record.raw_user_meta_data->>'full_name', split_part(user_record.email, '@', 1)) || '''s Workspace')
    RETURNING id INTO new_workspace_id;
    
    -- Add user as admin
    INSERT INTO workspace_users (workspace_id, user_id, role)
    VALUES (new_workspace_id, user_record.id, 'admin');
    
    -- Create default categories if they don't exist
    INSERT INTO categories (name, scope, user_id)
    VALUES 
      ('Food & Dining', 'personal', user_record.id),
      ('Transportation', 'personal', user_record.id),
      ('Shopping', 'personal', user_record.id),
      ('Entertainment', 'personal', user_record.id),
      ('Bills & Utilities', 'personal', user_record.id),
      ('Healthcare', 'personal', user_record.id),
      ('Education', 'personal', user_record.id),
      ('Travel', 'personal', user_record.id),
      ('Personal Care', 'personal', user_record.id),
      ('Gifts & Donations', 'personal', user_record.id),
      ('Office Supplies', 'business', user_record.id),
      ('Marketing', 'business', user_record.id),
      ('Payroll', 'business', user_record.id),
      ('Software & Tools', 'business', user_record.id),
      ('Professional Services', 'business', user_record.id),
      ('Travel & Expenses', 'business', user_record.id),
      ('Equipment', 'business', user_record.id),
      ('Insurance', 'both', user_record.id),
      ('Taxes', 'both', user_record.id),
      ('Other', 'both', user_record.id)
    ON CONFLICT (name) DO NOTHING;
    
    -- Create default buckets with target percentages
    INSERT INTO buckets (name, workspace_id, target_pct)
    VALUES 
      ('Emergency Fund', new_workspace_id, 20),
      ('Vacation', new_workspace_id, 10),
      ('Home Improvement', new_workspace_id, 10),
      ('Car Maintenance', new_workspace_id, 5),
      ('Christmas', new_workspace_id, 5),
      ('Medical', new_workspace_id, 10),
      ('Clothing', new_workspace_id, 5),
      ('Fun Money', new_workspace_id, 10);
  END LOOP;
END $$; 