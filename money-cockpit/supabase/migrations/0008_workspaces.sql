-- 0008_workspaces.sql - Multi-user workspace support

-- 1.1 Workspaces table
create table workspaces (
  id uuid primary key default uuid_generate_v4(),
  name text not null,
  created_at timestamptz default now()
);

-- 1.2 Workspace-Users link table
create table workspace_users (
  workspace_id uuid references workspaces on delete cascade,
  user_id uuid references auth.users on delete cascade,
  role text default 'admin' check (role in ('admin', 'viewer')),
  created_at timestamptz default now(),
  primary key (workspace_id, user_id)
);

-- 1.3 Add workspace_id to core tables
alter table entities add column workspace_id uuid references workspaces;
alter table accounts add column workspace_id uuid references workspaces;
alter table transactions add column workspace_id uuid references workspaces;
alter table debts add column workspace_id uuid references workspaces;
alter table recurring_expenses add column workspace_id uuid references workspaces;
alter table income_streams add column workspace_id uuid references workspaces;
alter table invoices_uploaded add column workspace_id uuid references workspaces;
alter table budgets add column workspace_id uuid references workspaces;
alter table goals add column workspace_id uuid references workspaces;
alter table categories add column workspace_id uuid references workspaces;
alter table buckets add column workspace_id uuid references workspaces;

-- 1.4 Migrate existing data
-- Create default workspace for existing users
do $$
declare
  default_workspace_id uuid;
  user_record record;
begin
  -- Create a default workspace
  insert into workspaces(name) values ('Default Workspace') returning id into default_workspace_id;
  
  -- Add all existing users to the default workspace as admins
  for user_record in select id from auth.users loop
    insert into workspace_users(workspace_id, user_id, role) 
    values (default_workspace_id, user_record.id, 'admin');
  end loop;
  
  -- Update all existing data to belong to the default workspace
  update entities set workspace_id = default_workspace_id;
  update accounts set workspace_id = default_workspace_id;
  update transactions set workspace_id = default_workspace_id;
  update debts set workspace_id = default_workspace_id;
  update recurring_expenses set workspace_id = default_workspace_id;
  update income_streams set workspace_id = default_workspace_id;
  update invoices_uploaded set workspace_id = default_workspace_id;
  update budgets set workspace_id = default_workspace_id;
  update goals set workspace_id = default_workspace_id;
  update categories set workspace_id = default_workspace_id;
  update buckets set workspace_id = default_workspace_id;
end $$;

-- 1.5 Make workspace_id required after migration
alter table entities alter column workspace_id set not null;
alter table accounts alter column workspace_id set not null;
alter table transactions alter column workspace_id set not null;
alter table debts alter column workspace_id set not null;
alter table recurring_expenses alter column workspace_id set not null;
alter table income_streams alter column workspace_id set not null;
alter table invoices_uploaded alter column workspace_id set not null;
alter table budgets alter column workspace_id set not null;
alter table goals alter column workspace_id set not null;
alter table categories alter column workspace_id set not null;
alter table buckets alter column workspace_id set not null;

-- 2. Row-Level Security Policies

-- Enable RLS on new tables
alter table workspaces enable row level security;
alter table workspace_users enable row level security;

-- Workspace policies
create policy "workspace_read" on workspaces for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = workspaces.id 
  and user_id = auth.uid()
));

create policy "workspace_admin_write" on workspaces for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = workspaces.id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Workspace users policies
create policy "workspace_users_read" on workspace_users for select
using (exists (
  select 1 from workspace_users wu2
  where wu2.workspace_id = workspace_users.workspace_id 
  and wu2.user_id = auth.uid()
));

create policy "workspace_users_admin_write" on workspace_users for insert, update, delete
using (exists (
  select 1 from workspace_users wu2
  where wu2.workspace_id = workspace_users.workspace_id 
  and wu2.user_id = auth.uid() 
  and wu2.role = 'admin'
));

-- Drop existing RLS policies and create workspace-aware ones

-- Entities
drop policy if exists "entities_policy" on entities;
create policy "entities_workspace_read" on entities for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = entities.workspace_id 
  and user_id = auth.uid()
));

create policy "entities_workspace_write" on entities for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = entities.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Accounts
drop policy if exists "accounts_policy" on accounts;
create policy "accounts_workspace_read" on accounts for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = accounts.workspace_id 
  and user_id = auth.uid()
));

create policy "accounts_workspace_write" on accounts for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = accounts.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Transactions
drop policy if exists "transactions_policy" on transactions;
create policy "transactions_workspace_read" on transactions for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = transactions.workspace_id 
  and user_id = auth.uid()
));

create policy "transactions_workspace_write" on transactions for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = transactions.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Debts
drop policy if exists "debts_policy" on debts;
create policy "debts_workspace_read" on debts for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = debts.workspace_id 
  and user_id = auth.uid()
));

create policy "debts_workspace_write" on debts for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = debts.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Recurring Expenses
drop policy if exists "recurring_expenses_policy" on recurring_expenses;
create policy "recurring_expenses_workspace_read" on recurring_expenses for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = recurring_expenses.workspace_id 
  and user_id = auth.uid()
));

create policy "recurring_expenses_workspace_write" on recurring_expenses for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = recurring_expenses.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Income Streams
drop policy if exists "income_streams_policy" on income_streams;
create policy "income_streams_workspace_read" on income_streams for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = income_streams.workspace_id 
  and user_id = auth.uid()
));

create policy "income_streams_workspace_write" on income_streams for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = income_streams.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Invoices
drop policy if exists "invoices_uploaded_policy" on invoices_uploaded;
create policy "invoices_workspace_read" on invoices_uploaded for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = invoices_uploaded.workspace_id 
  and user_id = auth.uid()
));

create policy "invoices_workspace_write" on invoices_uploaded for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = invoices_uploaded.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Budgets
drop policy if exists "budgets_policy" on budgets;
create policy "budgets_workspace_read" on budgets for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = budgets.workspace_id 
  and user_id = auth.uid()
));

create policy "budgets_workspace_write" on budgets for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = budgets.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Goals
drop policy if exists "goals_policy" on goals;
create policy "goals_workspace_read" on goals for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = goals.workspace_id 
  and user_id = auth.uid()
));

create policy "goals_workspace_write" on goals for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = goals.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Categories
drop policy if exists "categories_policy" on categories;
create policy "categories_workspace_read" on categories for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = categories.workspace_id 
  and user_id = auth.uid()
));

create policy "categories_workspace_write" on categories for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = categories.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- Buckets
drop policy if exists "buckets_policy" on buckets;
create policy "buckets_workspace_read" on buckets for select
using (exists (
  select 1 from workspace_users 
  where workspace_id = buckets.workspace_id 
  and user_id = auth.uid()
));

create policy "buckets_workspace_write" on buckets for insert, update, delete
using (exists (
  select 1 from workspace_users 
  where workspace_id = buckets.workspace_id 
  and user_id = auth.uid() 
  and role = 'admin'
));

-- 3. Helper functions

-- Get user workspaces
create or replace function get_user_workspaces()
returns table(
  workspace_id uuid,
  workspace_name text,
  user_role text
)
language sql
security definer
as $$
  select 
    w.id as workspace_id,
    w.name as workspace_name,
    wu.role as user_role
  from workspaces w
  join workspace_users wu on w.id = wu.workspace_id
  where wu.user_id = auth.uid();
$$;

-- Check if user is admin of workspace
create or replace function is_workspace_admin(workspace_uuid uuid)
returns boolean
language sql
security definer
as $$
  select exists(
    select 1 from workspace_users
    where workspace_id = workspace_uuid
    and user_id = auth.uid()
    and role = 'admin'
  );
$$;

-- Update get_dashboard function to be workspace-aware
create or replace function get_dashboard(p_lens text, p_workspace_id uuid)
returns json
language plpgsql
security definer
as $$
declare
    result json;
    user_id uuid;
begin
    user_id := auth.uid();
    
    -- Verify user has access to workspace
    if not exists(
        select 1 from workspace_users 
        where workspace_id = p_workspace_id 
        and user_id = user_id
    ) then
        raise exception 'Access denied to workspace';
    end if;
    
    with filtered_entities as (
        select id from entities 
        where workspace_id = p_workspace_id
        and is_active = true
        and (
            p_lens = 'all' or 
            (p_lens = 'personal' and type = 'personal') or
            (p_lens != 'personal' and p_lens != 'all' and name = p_lens)
        )
    ),
    account_balances as (
        select 
            a.entity_id,
            sum(t.amount) as balance,
            t.currency
        from accounts a
        join transactions t on t.account_id = a.id
        where a.entity_id in (select id from filtered_entities)
        group by a.entity_id, t.currency
    ),
    debt_totals as (
        select 
            sum(balance) as total_debt
        from debts
        where entity_id in (select id from filtered_entities)
    ),
    upcoming_payments as (
        select 
            sum(amount) as total_upcoming
        from recurring_expenses
        where entity_id in (select id from filtered_entities)
        and next_due <= current_date + interval '14 days'
    )
    select json_build_object(
        'net_worth', coalesce((select sum(balance) from account_balances), 0) - coalesce((select total_debt from debt_totals), 0),
        'total_assets', coalesce((select sum(balance) from account_balances), 0),
        'total_debt', coalesce((select total_debt from debt_totals), 0),
        'upcoming_payments', coalesce((select total_upcoming from upcoming_payments), 0),
        'lens', p_lens,
        'workspace_id', p_workspace_id
    ) into result;
    
    return result;
end;
$$; 