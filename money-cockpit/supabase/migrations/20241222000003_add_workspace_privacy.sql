-- Add is_private column to workspaces table and update create_workspace function

-- 1. Add is_private column to workspaces table
ALTER TABLE workspaces ADD COLUMN IF NOT EXISTS is_private boolean DEFAULT false;

-- 2. Update the create_workspace function to support is_private parameter
CREATE OR REPLACE FUNCTION create_workspace(p_name text, p_user_id uuid, p_is_private boolean DEFAULT false)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_workspace_id uuid;
  v_result json;
BEGIN
  -- Validate inputs
  IF p_name IS NULL OR trim(p_name) = '' THEN
    RAISE EXCEPTION 'Workspace name is required';
  END IF;
  
  -- Create the workspace
  INSERT INTO workspaces (name, is_private)
  VALUES (p_name, p_is_private)
  RETURNING id INTO v_workspace_id;
  
  -- Add the user as admin
  INSERT INTO workspace_users (workspace_id, user_id, role)
  VALUES (v_workspace_id, p_user_id, 'admin');
  
  -- Return the created workspace
  SELECT json_build_object(
    'id', id,
    'name', name,
    'is_private', is_private,
    'created_at', created_at
  ) INTO v_result
  FROM workspaces
  WHERE id = v_workspace_id;
  
  RETURN v_result;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Failed to create workspace: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_workspace(text, uuid, boolean) TO authenticated;

-- 3. Update get_user_workspaces function to include is_private
CREATE OR REPLACE FUNCTION get_user_workspaces()
RETURNS TABLE(
  workspace_id uuid,
  workspace_name text,
  user_role text,
  is_private boolean
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    w.id as workspace_id,
    w.name as workspace_name,
    wu.role as user_role,
    w.is_private
  FROM workspaces w
  JOIN workspace_users wu on w.id = wu.workspace_id
  WHERE wu.user_id = auth.uid();
$$;
