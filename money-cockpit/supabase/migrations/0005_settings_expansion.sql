-- 1.1 Categories master table
-- First check if category_rules exists and rename it, otherwise use existing categories table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'category_rules') THEN
        ALTER TABLE category_rules RENAME TO categories;
    END IF;
END $$;

-- Add new columns to categories if they don't exist
ALTER TABLE categories ADD COLUMN IF NOT EXISTS scope TEXT DEFAULT 'both' CHECK (scope IN ('personal', 'business', 'both'));
ALTER TABLE categories ADD COLUMN IF NOT EXISTS gst_default BOOLEAN DEFAULT false;
ALTER TABLE categories ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE categories ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- 1.2 Buckets already exist; ensure they have the right structure
CREATE TABLE IF NOT EXISTS buckets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    target_pct NUMERIC(5,2) NOT NULL CHECK (target_pct >= 0 AND target_pct <= 100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, name)
);

-- 1.3 Add user preferences
CREATE TABLE IF NOT EXISTS user_prefs (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    pay_day TEXT DEFAULT 'Friday' CHECK (pay_day IN ('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')),
    default_lens TEXT DEFAULT 'personal',
    date_fmt TEXT DEFAULT 'd MMM',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 1.4 Ensure entities has is_active
ALTER TABLE entities ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Enable RLS on new tables
ALTER TABLE buckets ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_prefs ENABLE ROW LEVEL SECURITY;

-- RLS policies for categories
CREATE POLICY "Users can view own categories" ON categories
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can create own categories" ON categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own categories" ON categories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own categories" ON categories
    FOR DELETE USING (auth.uid() = user_id);

-- RLS policies for buckets
CREATE POLICY "Users can view own buckets" ON buckets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own buckets" ON buckets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own buckets" ON buckets
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own buckets" ON buckets
    FOR DELETE USING (auth.uid() = user_id);

-- RLS policies for user_prefs
CREATE POLICY "Users can view own preferences" ON user_prefs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own preferences" ON user_prefs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON user_prefs
    FOR UPDATE USING (auth.uid() = user_id);

-- Function to ensure default buckets exist for a user
CREATE OR REPLACE FUNCTION ensure_default_buckets(p_user_id UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO buckets (user_id, name, target_pct)
    VALUES 
        (p_user_id, 'Daily', 60),
        (p_user_id, 'Splurge', 10),
        (p_user_id, 'Smile', 10),
        (p_user_id, 'Fire', 20)
    ON CONFLICT (user_id, name) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get or create user preferences
CREATE OR REPLACE FUNCTION get_or_create_user_prefs(p_user_id UUID)
RETURNS user_prefs AS $$
DECLARE
    v_prefs user_prefs;
BEGIN
    -- Try to get existing prefs
    SELECT * INTO v_prefs FROM user_prefs WHERE user_id = p_user_id;
    
    -- If not found, create default prefs
    IF NOT FOUND THEN
        INSERT INTO user_prefs (user_id)
        VALUES (p_user_id)
        RETURNING * INTO v_prefs;
    END IF;
    
    RETURN v_prefs;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 