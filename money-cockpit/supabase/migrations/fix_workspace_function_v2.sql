-- Fix missing get_user_workspaces function
-- This script adds the workspace function that's needed for the app to work

-- First, let's check if the workspaces tables exist, if not create them
CREATE TABLE IF NOT EXISTS workspaces (
  id uuid primary key default uuid_generate_v4(),
  name text not null,
  created_at timestamptz default now()
);

CREATE TABLE IF NOT EXISTS workspace_users (
  workspace_id uuid references workspaces on delete cascade,
  user_id uuid references auth.users on delete cascade,
  role text default 'admin' check (role in ('admin', 'viewer')),
  created_at timestamptz default now(),
  primary key (workspace_id, user_id)
);

-- Enable RLS on workspace tables if not already enabled
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_users ENABLE ROW LEVEL SECURITY;

-- Create the missing get_user_workspaces function
CREATE OR REPLACE FUNCTION get_user_workspaces()
RETURNS TABLE(
  workspace_id uuid,
  workspace_name text,
  user_role text
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    w.id as workspace_id,
    w.name as workspace_name,
    wu.role as user_role
  FROM workspaces w
  JOIN workspace_users wu on w.id = wu.workspace_id
  WHERE wu.user_id = auth.uid();
$$;

-- Create the is_workspace_admin function if it doesn't exist
CREATE OR REPLACE FUNCTION is_workspace_admin(workspace_uuid uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT exists(
    SELECT 1 FROM workspace_users
    WHERE workspace_id = workspace_uuid
    AND user_id = auth.uid()
    AND role = 'admin'
  );
$$;

-- Create workspace policies if they don't exist
DO $$
BEGIN
  -- Workspace read policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'workspaces' 
    AND policyname = 'workspace_read'
  ) THEN
    CREATE POLICY "workspace_read" ON workspaces FOR SELECT
    USING (EXISTS (
      SELECT 1 FROM workspace_users 
      WHERE workspace_id = workspaces.id 
      AND user_id = auth.uid()
    ));
  END IF;

  -- Workspace admin write policies (separate for each operation)
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'workspaces' 
    AND policyname = 'workspace_admin_insert'
  ) THEN
    CREATE POLICY "workspace_admin_insert" ON workspaces FOR INSERT
    WITH CHECK (EXISTS (
      SELECT 1 FROM workspace_users 
      WHERE workspace_id = workspaces.id 
      AND user_id = auth.uid() 
      AND role = 'admin'
    ));
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'workspaces' 
    AND policyname = 'workspace_admin_update'
  ) THEN
    CREATE POLICY "workspace_admin_update" ON workspaces FOR UPDATE
    USING (EXISTS (
      SELECT 1 FROM workspace_users 
      WHERE workspace_id = workspaces.id 
      AND user_id = auth.uid() 
      AND role = 'admin'
    ));
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'workspaces' 
    AND policyname = 'workspace_admin_delete'
  ) THEN
    CREATE POLICY "workspace_admin_delete" ON workspaces FOR DELETE
    USING (EXISTS (
      SELECT 1 FROM workspace_users 
      WHERE workspace_id = workspaces.id 
      AND user_id = auth.uid() 
      AND role = 'admin'
    ));
  END IF;

  -- Workspace users read policy
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'workspace_users' 
    AND policyname = 'workspace_users_read'
  ) THEN
    CREATE POLICY "workspace_users_read" ON workspace_users FOR SELECT
    USING (EXISTS (
      SELECT 1 FROM workspace_users wu2
      WHERE wu2.workspace_id = workspace_users.workspace_id 
      AND wu2.user_id = auth.uid()
    ));
  END IF;

  -- Workspace users admin write policies (separate for each operation)
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'workspace_users' 
    AND policyname = 'workspace_users_admin_insert'
  ) THEN
    CREATE POLICY "workspace_users_admin_insert" ON workspace_users FOR INSERT
    WITH CHECK (EXISTS (
      SELECT 1 FROM workspace_users wu2
      WHERE wu2.workspace_id = workspace_users.workspace_id 
      AND wu2.user_id = auth.uid() 
      AND wu2.role = 'admin'
    ));
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'workspace_users' 
    AND policyname = 'workspace_users_admin_update'
  ) THEN
    CREATE POLICY "workspace_users_admin_update" ON workspace_users FOR UPDATE
    USING (EXISTS (
      SELECT 1 FROM workspace_users wu2
      WHERE wu2.workspace_id = workspace_users.workspace_id 
      AND wu2.user_id = auth.uid() 
      AND wu2.role = 'admin'
    ));
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'workspace_users' 
    AND policyname = 'workspace_users_admin_delete'
  ) THEN
    CREATE POLICY "workspace_users_admin_delete" ON workspace_users FOR DELETE
    USING (EXISTS (
      SELECT 1 FROM workspace_users wu2
      WHERE wu2.workspace_id = workspace_users.workspace_id 
      AND wu2.user_id = auth.uid() 
      AND wu2.role = 'admin'
    ));
  END IF;
END $$;

-- Create a default workspace for existing users if none exist
DO $$
DECLARE
  default_workspace_id uuid;
  user_record record;
  workspace_count integer;
BEGIN
  -- Check if any workspaces exist
  SELECT COUNT(*) INTO workspace_count FROM workspaces;
  
  -- If no workspaces exist, create default workspace and add all users
  IF workspace_count = 0 THEN
    -- Create a default workspace
    INSERT INTO workspaces(name) VALUES ('Default Workspace') RETURNING id INTO default_workspace_id;
    
    -- Add all existing users to the default workspace as admins
    FOR user_record IN SELECT id FROM auth.users LOOP
      INSERT INTO workspace_users(workspace_id, user_id, role) 
      VALUES (default_workspace_id, user_record.id, 'admin')
      ON CONFLICT (workspace_id, user_id) DO NOTHING;
    END LOOP;
  END IF;
END $$; 