-- Add debt linking to transactions
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS debt_id UUID REFERENCES debts(id);

-- Enhance debts table with payment tracking
ALTER TABLE debts
ADD COLUMN IF NOT EXISTS expected_freq TEXT CHECK (expected_freq IN ('weekly', 'monthly', 'quarterly', 'annually')),
ADD COLUMN IF NOT EXISTS grace_days INTEGER DEFAULT 5,
ADD COLUMN IF NOT EXISTS last_paid DATE;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_transactions_debt_id ON transactions(debt_id);
CREATE INDEX IF NOT EXISTS idx_debts_last_paid ON debts(last_paid); 