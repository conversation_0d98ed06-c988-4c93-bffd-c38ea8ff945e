-- 1.1 Add optional owner_user_id + shared flag to accounts & transactions
alter table accounts
  add column if not exists owner_user_id uuid references auth.users,
  add column if not exists is_shared boolean default true;

alter table transactions
  add column if not exists owner_user_id uuid references auth.users,
  add column if not exists is_shared boolean default true;

-- Only meaningful when entity.type = 'personal'
-- Back-fill existing rows => shared = true
update accounts set is_shared = true where is_shared is null;
update transactions set is_shared = true where is_shared is null;

-- 1.2 Helper view for reporting (optional)
create or replace view v_personal_spending as
select
  coalesce(u.email, 'shared') as owner,
  date_trunc('month', t.date) as month,
  sum(abs(t.amount)) as spend
from transactions t
left join auth.users u on u.id = t.owner_user_id
join entities e on e.id = t.entity_id
where e.type = 'personal'
group by 1,2; 