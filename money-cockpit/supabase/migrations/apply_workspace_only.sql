-- Apply workspace functionality only - safe to run multiple times
-- Run this in Supabase Dashboard > SQL Editor

-- 1. Create workspaces table if it doesn't exist
CREATE TABLE IF NOT EXISTS workspaces (
  id uuid primary key default uuid_generate_v4(),
  name text not null,
  created_at timestamptz default now()
);

-- 2. Create workspace_users table if it doesn't exist
CREATE TABLE IF NOT EXISTS workspace_users (
  workspace_id uuid references workspaces on delete cascade,
  user_id uuid references auth.users on delete cascade,
  role text default 'admin' check (role in ('admin', 'viewer')),
  created_at timestamptz default now(),
  primary key (workspace_id, user_id)
);

-- 3. Add workspace_id columns if they don't exist
ALTER TABLE entities ADD COLUMN IF NOT EXISTS workspace_id uuid references workspaces;
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS workspace_id uuid references workspaces;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS workspace_id uuid references workspaces;
ALTER TABLE debts ADD COLUMN IF NOT EXISTS workspace_id uuid references workspaces;
ALTER TABLE recurring_expenses ADD COLUMN IF NOT EXISTS workspace_id uuid references workspaces;
ALTER TABLE income_streams ADD COLUMN IF NOT EXISTS workspace_id uuid references workspaces;
ALTER TABLE goals ADD COLUMN IF NOT EXISTS workspace_id uuid references workspaces;

-- Check if invoices_uploaded table exists, if not create it
CREATE TABLE IF NOT EXISTS invoices_uploaded (
  id uuid primary key default uuid_generate_v4(),
  entity_id uuid not null references entities(id) on delete cascade,
  filename text not null,
  file_path text not null,
  file_size integer,
  mime_type text,
  parsed_amount numeric,
  parsed_due_date date,
  parsed_vendor text,
  matched_transaction_id uuid references transactions(id),
  uploaded_at timestamptz default now(),
  workspace_id uuid references workspaces
);

-- Check if budgets table exists, if not create it
CREATE TABLE IF NOT EXISTS budgets (
  id uuid primary key default uuid_generate_v4(),
  entity_id uuid not null references entities(id) on delete cascade,
  name text not null,
  amount numeric not null,
  period text not null check (period in ('weekly', 'monthly', 'yearly')),
  category text,
  start_date date not null,
  end_date date,
  is_active boolean default true,
  created_at timestamptz default now(),
  workspace_id uuid references workspaces
);

-- Check if categories table exists with workspace_id
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'categories' AND column_name = 'workspace_id') THEN
    ALTER TABLE categories ADD COLUMN workspace_id uuid references workspaces;
  END IF;
END $$;

-- Check if buckets table exists with workspace_id
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'buckets' AND column_name = 'workspace_id') THEN
    ALTER TABLE buckets ADD COLUMN workspace_id uuid references workspaces;
  END IF;
END $$;

-- 4. Migrate existing data to default workspace (only if no workspace exists)
DO $$
DECLARE
  default_workspace_id uuid;
  user_record record;
  workspace_count integer;
BEGIN
  -- Check if any workspaces exist
  SELECT COUNT(*) INTO workspace_count FROM workspaces;
  
  IF workspace_count = 0 THEN
    -- Create a default workspace
    INSERT INTO workspaces(name) VALUES ('Default Workspace') RETURNING id INTO default_workspace_id;
    
    -- Add all existing users to the default workspace as admins
    FOR user_record IN SELECT id FROM auth.users LOOP
      INSERT INTO workspace_users(workspace_id, user_id, role) 
      VALUES (default_workspace_id, user_record.id, 'admin')
      ON CONFLICT DO NOTHING;
    END LOOP;
    
    -- Update all existing data to belong to the default workspace
    UPDATE entities SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE accounts SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE transactions SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE debts SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE recurring_expenses SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE income_streams SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE goals SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE invoices_uploaded SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE budgets SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE categories SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
    UPDATE buckets SET workspace_id = default_workspace_id WHERE workspace_id IS NULL;
  END IF;
END $$;

-- 5. Enable RLS on new tables
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_users ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "workspace_read" ON workspaces;
CREATE POLICY "workspace_read" ON workspaces FOR SELECT
USING (EXISTS (
  SELECT 1 FROM workspace_users 
  WHERE workspace_id = workspaces.id 
  AND user_id = auth.uid()
));

DROP POLICY IF EXISTS "workspace_admin_write" ON workspaces;
CREATE POLICY "workspace_admin_write" ON workspaces FOR INSERT, UPDATE, DELETE
USING (EXISTS (
  SELECT 1 FROM workspace_users 
  WHERE workspace_id = workspaces.id 
  AND user_id = auth.uid() 
  AND role = 'admin'
));

DROP POLICY IF EXISTS "workspace_users_read" ON workspace_users;
CREATE POLICY "workspace_users_read" ON workspace_users FOR SELECT
USING (EXISTS (
  SELECT 1 FROM workspace_users wu2
  WHERE wu2.workspace_id = workspace_users.workspace_id 
  AND wu2.user_id = auth.uid()
));

DROP POLICY IF EXISTS "workspace_users_admin_write" ON workspace_users;
CREATE POLICY "workspace_users_admin_write" ON workspace_users FOR INSERT, UPDATE, DELETE
USING (EXISTS (
  SELECT 1 FROM workspace_users wu2
  WHERE wu2.workspace_id = workspace_users.workspace_id 
  AND wu2.user_id = auth.uid() 
  AND wu2.role = 'admin'
));

-- 7. Update existing RLS policies for workspace awareness
-- Entities
DROP POLICY IF EXISTS "entities_workspace_read" ON entities;
CREATE POLICY "entities_workspace_read" ON entities FOR SELECT
USING (EXISTS (
  SELECT 1 FROM workspace_users 
  WHERE workspace_id = entities.workspace_id 
  AND user_id = auth.uid()
));

DROP POLICY IF EXISTS "entities_workspace_write" ON entities;
CREATE POLICY "entities_workspace_write" ON entities FOR INSERT, UPDATE, DELETE
USING (EXISTS (
  SELECT 1 FROM workspace_users 
  WHERE workspace_id = entities.workspace_id 
  AND user_id = auth.uid() 
  AND role = 'admin'
));

-- 8. Create helper functions
CREATE OR REPLACE FUNCTION get_user_workspaces()
RETURNS TABLE(
  workspace_id uuid,
  workspace_name text,
  user_role text
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    w.id as workspace_id,
    w.name as workspace_name,
    wu.role as user_role
  FROM workspaces w
  JOIN workspace_users wu on w.id = wu.workspace_id
  WHERE wu.user_id = auth.uid();
$$;

CREATE OR REPLACE FUNCTION is_workspace_admin(workspace_uuid uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT exists(
    SELECT 1 FROM workspace_users
    WHERE workspace_id = workspace_uuid
    AND user_id = auth.uid()
    AND role = 'admin'
  );
$$;

-- 9. Update get_dashboard function to be workspace-aware
CREATE OR REPLACE FUNCTION get_dashboard(p_lens text, p_workspace_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
    user_id uuid;
BEGIN
    user_id := auth.uid();
    
    -- Verify user has access to workspace
    IF NOT EXISTS(
        SELECT 1 FROM workspace_users 
        WHERE workspace_id = p_workspace_id 
        AND user_id = user_id
    ) THEN
        RAISE EXCEPTION 'Access denied to workspace';
    END IF;
    
    WITH filtered_entities AS (
        SELECT id FROM entities 
        WHERE workspace_id = p_workspace_id
        AND is_active = true
        AND (
            p_lens = 'all' OR 
            (p_lens = 'personal' AND type = 'personal') OR
            (p_lens != 'personal' AND p_lens != 'all' AND name = p_lens)
        )
    ),
    account_balances AS (
        SELECT 
            a.entity_id,
            SUM(t.amount) as balance,
            t.currency
        FROM accounts a
        JOIN transactions t ON t.account_id = a.id
        WHERE a.entity_id IN (SELECT id FROM filtered_entities)
        GROUP BY a.entity_id, t.currency
    ),
    debt_totals AS (
        SELECT 
            SUM(balance) as total_debt
        FROM debts
        WHERE entity_id IN (SELECT id FROM filtered_entities)
    ),
    upcoming_payments AS (
        SELECT 
            SUM(amount) as total_upcoming
        FROM recurring_expenses
        WHERE entity_id IN (SELECT id FROM filtered_entities)
        AND next_due <= CURRENT_DATE + INTERVAL '14 days'
    )
    SELECT json_build_object(
        'net_worth', COALESCE((SELECT SUM(balance) FROM account_balances), 0) - COALESCE((SELECT total_debt FROM debt_totals), 0),
        'total_assets', COALESCE((SELECT SUM(balance) FROM account_balances), 0),
        'total_debt', COALESCE((SELECT total_debt FROM debt_totals), 0),
        'upcoming_payments', COALESCE((SELECT total_upcoming FROM upcoming_payments), 0),
        'lens', p_lens,
        'workspace_id', p_workspace_id
    ) INTO result;
    
    RETURN result;
END;
$$;

-- Show results
SELECT 'Workspace setup completed successfully!' as status;
SELECT COUNT(*) as workspace_count FROM workspaces;
SELECT COUNT(*) as workspace_users_count FROM workspace_users; 