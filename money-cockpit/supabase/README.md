# Supabase Directory - Database & Edge Functions

This directory contains all Supabase-related configurations, database migrations, and edge functions that power the MoneyMofos backend infrastructure.

## 📁 Directory Structure

### `/migrations` - Database Schema
SQL migration files that define the database structure:
- **0001_initial_schema.sql**: Core tables (users, entities, accounts)
- **0002_transactions.sql**: Transaction tracking system
- **0003_budgets_goals.sql**: Budget and goal management
- **0004_invoices.sql**: Invoice management system
- **0005_recurring_expenses.sql**: Recurring expense automation
- **0006_achievements.sql**: Gamification system
- **0007_insights.sql**: Financial insights and analytics
- **0008_workspaces.sql**: Multi-tenant workspace system

### `/functions` - Edge Functions
Serverless functions for business logic:
- **bucket_sweeper**: Automated bucket balance management
- **budget_snapshot**: Daily budget status snapshots
- **cfo_digest**: Weekly financial summary emails
- **insert_workspace_user**: User invitation webhook handler
- **interest_accrual**: Daily interest calculation for debts
- **invoice_matcher**: Automatic invoice-transaction matching
- **missed_payment_checker**: Overdue payment notifications
- **ocr_parse_invoice**: Invoice OCR processing
- **recurring_expenses_post**: Automated recurring expense creation

## 🗄️ Database Schema

### Core Tables

#### Workspaces & Users
```sql
-- Multi-tenant workspace system
CREATE TABLE workspaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Workspace membership with roles
CREATE TABLE workspace_users (
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('admin', 'viewer')),
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (workspace_id, user_id)
);
```

#### Entities & Accounts
```sql
-- Business entities (personal/business)
CREATE TABLE entities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('personal', 'business')),
  abn TEXT,
  is_active BOOLEAN DEFAULT TRUE
);

-- Financial accounts
CREATE TABLE accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  balance DECIMAL(15,2) DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE
);
```

#### Transactions
```sql
-- Financial transactions
CREATE TABLE transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  entity_id UUID REFERENCES entities(id),
  account_id UUID REFERENCES accounts(id),
  amount DECIMAL(15,2) NOT NULL,
  description TEXT NOT NULL,
  category_id UUID REFERENCES categories(id),
  transaction_date TIMESTAMPTZ NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'transfer')),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Budgets & Goals
```sql
-- Budget management
CREATE TABLE budgets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  entity_id UUID REFERENCES entities(id),
  name TEXT NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  period TEXT NOT NULL CHECK (period IN ('weekly', 'monthly', 'yearly')),
  category TEXT,
  start_date DATE NOT NULL,
  end_date DATE,
  is_active BOOLEAN DEFAULT TRUE
);

-- Financial goals
CREATE TABLE goals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  entity_id UUID REFERENCES entities(id),
  name TEXT NOT NULL,
  target_amount DECIMAL(15,2) NOT NULL,
  current_amount DECIMAL(15,2) DEFAULT 0,
  target_date DATE,
  is_achieved BOOLEAN DEFAULT FALSE
);
```

### Row-Level Security (RLS)

All tables implement RLS policies for workspace isolation:

```sql
-- Example RLS policy for transactions
CREATE POLICY "Users can view workspace transactions" ON transactions
  FOR SELECT USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_users 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Admins can modify workspace transactions" ON transactions
  FOR ALL USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_users 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );
```

## ⚡ Edge Functions

### Business Logic Functions

#### Budget Snapshot (`budget_snapshot`)
Daily function that captures budget status:
```typescript
// Triggered daily via cron
// Calculates budget utilization
// Sends notifications for over-budget items
// Stores historical budget data
```

#### Interest Accrual (`interest_accrual`)
Daily compound interest calculation:
```typescript
// Processes all active debts
// Calculates daily compound interest
// Updates debt balances
// Tracks payment schedules
```

#### Recurring Expenses (`recurring_expenses_post`)
Automated recurring transaction creation:
```typescript
// Processes due recurring expenses
// Creates transactions automatically
// Updates next due dates
// Handles frequency calculations
```

### Integration Functions

#### Invoice Matcher (`invoice_matcher`)
Automatic invoice-transaction matching:
```typescript
// Matches uploaded invoices to transactions
// Uses amount and date proximity
// Updates invoice status
// Reduces manual reconciliation
```

#### OCR Parse Invoice (`ocr_parse_invoice`)
Invoice text extraction:
```typescript
// Processes uploaded PDF invoices
// Extracts key information (amount, date, vendor)
// Populates invoice fields automatically
// Integrates with external OCR services
```

#### Workspace User Insert (`insert_workspace_user`)
User invitation webhook:
```typescript
// Triggered on user signup
// Checks for pending invitations
// Automatically adds user to workspace
// Sends welcome notifications
```

### Notification Functions

#### CFO Digest (`cfo_digest`)
Weekly financial summary:
```typescript
// Generates comprehensive financial reports
// Calculates key metrics and trends
// Sends email summaries to workspace admins
// Includes budget alerts and recommendations
```

#### Missed Payment Checker (`missed_payment_checker`)
Overdue payment notifications:
```typescript
// Checks for overdue invoices and debts
// Sends reminder notifications
// Escalates based on overdue duration
// Updates payment status tracking
```

### Utility Functions

#### Bucket Sweeper (`bucket_sweeper`)
Automated bucket management:
```typescript
// Redistributes excess funds between buckets
// Maintains target bucket balances
// Handles overflow and deficit scenarios
// Optimizes cash flow allocation
```

## 🔧 Development Workflow

### Database Migrations
```bash
# Create new migration
supabase migration new migration_name

# Apply migrations locally
supabase db reset

# Push to remote
supabase db push
```

### Edge Function Development
```bash
# Create new function
supabase functions new function_name

# Serve locally
supabase functions serve

# Deploy function
supabase functions deploy function_name

# View logs
supabase functions logs function_name
```

### Environment Setup
```bash
# Initialize Supabase project
supabase init

# Link to remote project
supabase link --project-ref your-project-ref

# Generate TypeScript types
supabase gen types typescript --local > src/types/database.ts
```

## 🛡️ Security Implementation

### Authentication
- Supabase Auth handles user management
- JWT tokens for API access
- Email-based user invitations
- Session management and refresh

### Authorization
- Row-Level Security on all tables
- Workspace-based data isolation
- Role-based permissions (admin/viewer)
- Function-level access control

### Data Protection
- Encrypted data at rest
- SSL/TLS for data in transit
- Audit logging for sensitive operations
- Backup and disaster recovery

## 📊 Performance Optimization

### Database Optimization
```sql
-- Indexes for common queries
CREATE INDEX idx_transactions_workspace_date 
ON transactions(workspace_id, transaction_date DESC);

CREATE INDEX idx_workspace_users_lookup 
ON workspace_users(user_id, workspace_id);

-- Partial indexes for active records
CREATE INDEX idx_entities_active 
ON entities(workspace_id) WHERE is_active = TRUE;
```

### Function Optimization
- Connection pooling for database access
- Caching for frequently accessed data
- Batch processing for bulk operations
- Async processing for heavy tasks

### Monitoring
- Function execution metrics
- Database performance monitoring
- Error tracking and alerting
- Usage analytics and optimization

## 🔄 Data Flow

### User Actions
1. User performs action in frontend
2. Frontend calls Supabase API or Edge Function
3. RLS policies validate access
4. Data is processed and stored
5. Real-time updates sent to connected clients

### Automated Processes
1. Cron triggers edge functions
2. Functions process business logic
3. Database updates are made
4. Notifications sent if required
5. Audit logs created

### Integration Flow
1. External data received (webhooks, uploads)
2. Edge functions process and validate
3. Data transformed and stored
4. Related records updated
5. Users notified of changes

## 🧪 Testing Strategy

### Database Testing
```sql
-- Test data setup
INSERT INTO workspaces (id, name) VALUES 
('test-workspace', 'Test Workspace');

-- Test RLS policies
SET ROLE authenticated;
SET request.jwt.claims TO '{"sub": "test-user-id"}';
```

### Function Testing
```typescript
// Local testing with Deno
import { assertEquals } from 'https://deno.land/std/testing/asserts.ts'

Deno.test('function processes correctly', async () => {
  const result = await functionHandler(testRequest)
  assertEquals(result.status, 200)
})
```

### Integration Testing
- End-to-end workflow testing
- Database migration testing
- Function deployment testing
- Performance benchmarking

---

This Supabase setup provides a robust, scalable backend for the MoneyMofos application with proper security, automation, and monitoring capabilities. 