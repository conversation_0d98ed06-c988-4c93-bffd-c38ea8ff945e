import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Get all active debts
    const { data: debts, error: debtsError } = await supabase
      .from('debts')
      .select('*')
      .gt('current_balance', 0);

    if (debtsError) throw debtsError;

    console.log(`Processing interest for ${debts?.length || 0} debts`);

    // Process each debt
    for (const debt of debts || []) {
      const dailyRate = debt.interest_rate / 100 / 365;
      let interestAmount = 0;

      if (debt.compounding_frequency === 'daily') {
        // Daily compounding
        interestAmount = debt.current_balance * dailyRate;
      } else if (debt.compounding_frequency === 'monthly') {
        // Monthly compounding - only accrue on the same day each month
        const today = new Date();
        const lastAccrual = debt.last_interest_date ? new Date(debt.last_interest_date) : new Date(debt.created_at);
        
        // Check if it's been at least a month
        if (today.getDate() === lastAccrual.getDate() || 
            (today.getDate() === new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate() && 
             lastAccrual.getDate() > today.getDate())) {
          const monthlyRate = debt.interest_rate / 100 / 12;
          interestAmount = debt.current_balance * monthlyRate;
        }
      }

      // Get any debt payments made today
      const { data: todayPayments } = await supabase
        .from('transactions')
        .select('amount')
        .eq('debt_id', debt.id)
        .eq('date', new Date().toISOString().split('T')[0]);

      const todayPaymentTotal = todayPayments?.reduce((sum, txn) => sum + txn.amount, 0) || 0;

      if (interestAmount > 0) {
        // Update debt balance: add interest and subtract any payments made today
        const newBalance = debt.current_balance + interestAmount + todayPaymentTotal;
        
        const { error: updateError } = await supabase
          .from('debts')
          .update({ 
            current_balance: newBalance,
            last_interest_date: new Date().toISOString()
          })
          .eq('id', debt.id);

        if (updateError) {
          console.error(`Error updating debt ${debt.id}:`, updateError);
        } else {
          console.log(`Updated debt ${debt.id}: added ${interestAmount} interest, new balance: ${newBalance}`);
          
          // Create a transaction record for the interest
          const { error: txnError } = await supabase
            .from('transactions')
            .insert({
              user_id: debt.user_id,
              entity_id: debt.entity_id,
              type: 'expense',
              amount: interestAmount,
              description: `Interest on ${debt.creditor}`,
              category_id: null, // You might want to create an "Interest" category
              transaction_date: new Date().toISOString()
            });

          if (txnError) {
            console.error(`Error creating interest transaction:`, txnError);
          }
        }
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Processed interest for ${debts?.length || 0} debts` 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );
  } catch (error) {
    console.error('Error in interest_accrual:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    );
  }
}); 