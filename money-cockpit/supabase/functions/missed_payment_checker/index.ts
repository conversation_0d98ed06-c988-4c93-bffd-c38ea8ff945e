import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    const today = new Date().toISOString().split('T')[0];

    // Get all active debts with payment tracking
    const { data: debts, error: debtsError } = await supabase
      .from('debts')
      .select('*')
      .gt('current_balance', 0)
      .not('next_due_date', 'is', null);

    if (debtsError) throw debtsError;

    console.log(`Checking ${debts?.length || 0} debts for missed payments`);

    let missedPayments = 0;

    // Check each debt for missed payments
    for (const debt of debts || []) {
      if (!debt.next_due_date) continue;

      const dueDate = new Date(debt.next_due_date);
      const graceDays = debt.grace_days || 5;
      const graceEndDate = new Date(dueDate);
      graceEndDate.setDate(graceEndDate.getDate() + graceDays);

      const todayDate = new Date(today);
      const lastPaidDate = debt.last_paid ? new Date(debt.last_paid) : null;

      // Check if payment is overdue (past grace period) and not paid since due date
      if (todayDate > graceEndDate && (!lastPaidDate || lastPaidDate < dueDate)) {
        missedPayments++;
        
        // Create high-priority insight about missed payment
        const { error: insightError } = await supabase
          .from('agent_insights')
          .insert({
            user_id: debt.user_id,
            entity_id: debt.entity_id,
            insight_type: 'LateFeeRisk',
            severity: 'red',
            title: `Missed Payment: ${debt.creditor}`,
            content: `Payment for ${debt.creditor} was due on ${debt.next_due_date} and is now ${Math.floor((todayDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))} days overdue. Late fees may apply.`,
            metadata: {
              debt_id: debt.id,
              due_date: debt.next_due_date,
              days_overdue: Math.floor((todayDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)),
              amount: debt.current_balance
            }
          });

        if (insightError) {
          console.error(`Error creating insight for debt ${debt.id}:`, insightError);
        } else {
          console.log(`Created missed payment alert for debt ${debt.id} (${debt.creditor})`);
        }
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Checked ${debts?.length || 0} debts, found ${missedPayments} missed payments` 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );
  } catch (error) {
    console.error('Error in missed_payment_checker:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    );
  }
}); 