import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    console.log('Starting goal completion check...');

    // Get all active goals
    const { data: goals, error: goalsError } = await supabase
      .from('goals')
      .select(`
        *,
        entities(id, name)
      `)
      .eq('status', 'active');

    if (goalsError) throw goalsError;

    console.log(`Found ${goals?.length || 0} active goals to check`);

    let completedCount = 0;
    let errorCount = 0;

    for (const goal of goals || []) {
      try {
        let isCompleted = false;

        // Check amount-based goals
        if (goal.target_amount && goal.entity_id) {
          // Calculate current balance for the entity
          const { data: accounts } = await supabase
            .from('accounts')
            .select('id')
            .eq('entity_id', goal.entity_id);

          if (accounts && accounts.length > 0) {
            const accountIds = accounts.map(a => a.id);
            
            const { data: transactions } = await supabase
              .from('transactions')
              .select('amount')
              .in('account_id', accountIds);

            const currentAmount = transactions?.reduce((sum, t) => sum + t.amount, 0) || 0;
            
            if (currentAmount >= goal.target_amount) {
              isCompleted = true;
              console.log(`Goal "${goal.name}" completed by amount: ${currentAmount} >= ${goal.target_amount}`);
            }
          }
        }

        // Check date-based goals
        if (goal.target_date && !isCompleted) {
          const targetDate = new Date(goal.target_date);
          const today = new Date();
          
          if (today >= targetDate) {
            isCompleted = true;
            console.log(`Goal "${goal.name}" completed by date: ${today} >= ${targetDate}`);
          }
        }

        // Update goal status if completed
        if (isCompleted) {
          const { error: updateError } = await supabase
            .from('goals')
            .update({ status: 'hit' })
            .eq('id', goal.id);

          if (updateError) {
            console.error(`Error updating goal ${goal.id}:`, updateError);
            errorCount++;
            continue;
          }

          // Create celebration insight
          const { error: insightError } = await supabase
            .from('agent_insights')
            .insert({
              lens: goal.entity_id ? (goal.entities?.name || 'business') : 'personal',
              headline: `🎉 Goal '${goal.name}' achieved!`,
              body: goal.target_amount 
                ? `Congratulations! You've reached your target of $${goal.target_amount.toLocaleString()}.`
                : `Congratulations! You've reached your target date goal.`,
              severity: 'success'
            });

          if (insightError) {
            console.error(`Error creating insight for goal ${goal.id}:`, insightError);
          }

          completedCount++;
          console.log(`Successfully marked goal "${goal.name}" as completed`);
        }
      } catch (error) {
        console.error(`Error processing goal ${goal.id}:`, error);
        errorCount++;
      }
    }

    const result = {
      success: true,
      message: `Goal completion check completed. ${completedCount} goals marked as completed, ${errorCount} errors.`,
      completedGoals: completedCount,
      errors: errorCount,
      totalChecked: goals?.length || 0
    };

    console.log('Goal completion check result:', result);

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Goal completion check failed:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
}); 