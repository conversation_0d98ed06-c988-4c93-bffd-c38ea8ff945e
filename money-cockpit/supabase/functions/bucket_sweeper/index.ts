import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const today = new Date().toISOString().split('T')[0]

    // Get bucket configurations
    const { data: buckets, error: bucketsError } = await supabaseClient
      .from('buckets')
      .select('*')
      .order('name')

    if (bucketsError) throw bucketsError

    // Get today's salary/income transactions
    const { data: incomeTransactions, error: incomeError } = await supabaseClient
      .from('transactions')
      .select(`
        *,
        account:accounts!inner(
          entity_id
        )
      `)
      .eq('date', today)
      .eq('service_type', 'salary')
      .gt('amount', 0)

    if (incomeError) throw incomeError

    let processedCount = 0

    for (const income of incomeTransactions || []) {
      // Check if this income has already been bucketed
      const { data: existingBucketTxns } = await supabaseClient
        .from('transactions')
        .select('id')
        .eq('date', today)
        .eq('service_type', 'bucket_allocation')
        .eq('account_id', income.account_id)
        .limit(1)

      if (existingBucketTxns && existingBucketTxns.length > 0) {
        console.log(`Income ${income.id} already bucketed, skipping`)
        continue
      }

      // Calculate bucket allocations
      const totalIncome = income.amount
      const bucketAllocations = []

      for (const bucket of buckets || []) {
        const allocation = totalIncome * (bucket.target_pct / 100)
        
        if (allocation > 0) {
          bucketAllocations.push({
            account_id: income.account_id,
            amount: -allocation, // Negative to represent money going out to bucket
            currency: income.currency,
            date: today,
            category: 'Bucket Allocation',
            service_type: 'bucket_allocation',
            bucket: bucket.name,
            memo: `${bucket.name} allocation from salary`
          })

          // Create corresponding positive entry for the bucket
          bucketAllocations.push({
            account_id: income.account_id,
            amount: allocation, // Positive in the bucket
            currency: income.currency,
            date: today,
            category: bucket.name,
            service_type: 'bucket_deposit',
            bucket: bucket.name,
            memo: `Deposit to ${bucket.name} bucket`
          })
        }
      }

      // Insert all bucket transactions
      if (bucketAllocations.length > 0) {
        const { error: insertError } = await supabaseClient
          .from('transactions')
          .insert(bucketAllocations)

        if (insertError) {
          console.error(`Error creating bucket allocations for income ${income.id}:`, insertError)
        } else {
          processedCount++
        }
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Processed ${processedCount} income transactions into buckets` 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
}) 