import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    const today = new Date().toISOString().split('T')[0];

    // Get all users with budgets
    const { data: users, error: usersError } = await supabase
      .from('budgets')
      .select('user_id')
      .eq('is_active', true);

    if (usersError) throw usersError;

    const uniqueUsers = [...new Set(users?.map(u => u.user_id) || [])];
    console.log(`Creating budget snapshots for ${uniqueUsers.length} users`);

    let snapshotCount = 0;
    let errorCount = 0;

    for (const userId of uniqueUsers) {
      try {
        // Get budget status for this user
        const { data: budgetStatus, error: statusError } = await supabase
          .rpc('get_budget_status', { p_user_id: userId });

        if (statusError) {
          console.error(`Error getting budget status for user ${userId}:`, statusError);
          errorCount++;
          continue;
        }

        // Create snapshots for each budget
        for (const budget of budgetStatus || []) {
          const { error: snapshotError } = await supabase
            .from('budget_snapshots')
            .insert({
              snapshot_date: today,
              user_id: userId,
              entity_id: budget.entity_id,
              bucket: budget.bucket,
              category_id: budget.category_name ? 
                (await supabase
                  .from('categories')
                  .select('id')
                  .eq('name', budget.category_name)
                  .single()).data?.id : null,
              spent: budget.spent_amount,
              budget: budget.budget_amount,
              currency: 'AUD'
            })
            .select()
            .single();

          if (snapshotError) {
            // Check if it's a duplicate key error
            if (snapshotError.code === '23505') {
              console.log(`Snapshot already exists for budget ${budget.bucket || budget.category_name}`);
            } else {
              console.error(`Error creating snapshot:`, snapshotError);
              errorCount++;
            }
          } else {
            snapshotCount++;
          }
        }

        // Check for over-budget items and update AI insights
        const overBudgetItems = budgetStatus?.filter(b => b.status === 'over') || [];
        
        if (overBudgetItems.length > 0) {
          // Get the latest insight to append to
          const { data: latestInsight } = await supabase
            .from('agent_insights')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          if (latestInsight && latestInsight.created_at > new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) {
            // Update existing insight with budget warning
            const budgetWarnings = overBudgetItems.map(item => {
              const overAmount = item.spent_amount - item.budget_amount;
              return `${item.bucket || item.category_name} budget blown by $${overAmount.toFixed(2)}`;
            }).join('; ');

            const updatedContent = latestInsight.content + `\n\n⚠️ BUDGET ALERT: ${budgetWarnings}`;

            await supabase
              .from('agent_insights')
              .update({ content: updatedContent })
              .eq('id', latestInsight.id);
          }
        }
      } catch (error) {
        console.error(`Error processing user ${userId}:`, error);
        errorCount++;
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Created ${snapshotCount} budget snapshots for ${uniqueUsers.length} users, ${errorCount} errors`,
        snapshots: snapshotCount,
        errors: errorCount
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );
  } catch (error) {
    console.error('Error in budget_snapshot:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    );
  }
}); 