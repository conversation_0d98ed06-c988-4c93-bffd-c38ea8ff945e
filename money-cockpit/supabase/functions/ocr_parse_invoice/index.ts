import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { storage_path, entity_id, user_id } = await req.json();

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Download the file from storage
    const { data: fileData, error: downloadError } = await supabase.storage
      .from('docs')
      .download(storage_path);

    if (downloadError) throw downloadError;

    // Convert file to text (simplified OCR simulation)
    // In a real implementation, you would use Tesseract.js or similar
    const text = await extractTextFromFile(fileData);
    
    // Parse the extracted text for invoice details
    const parsedData = parseInvoiceText(text);

    // Insert the parsed invoice data
    const { error: insertError } = await supabase
      .from('invoices_uploaded')
      .insert({
        entity_id,
        user_id,
        storage_path,
        status: parsedData.confidence > 0.7 ? 'UNPAID' : 'ERROR',
        amount: parsedData.amount,
        due_date: parsedData.due_date,
        vendor: parsedData.vendor,
        category: parsedData.category
      });

    if (insertError) throw insertError;

    return new Response(
      JSON.stringify({ 
        success: true, 
        parsed_data: parsedData,
        message: 'Invoice parsed and stored successfully' 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );
  } catch (error) {
    console.error('Error in ocr_parse_invoice:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    );
  }
});

// Simplified text extraction (in reality, would use proper OCR)
async function extractTextFromFile(file: Blob): Promise<string> {
  // This is a placeholder - in a real implementation you would:
  // 1. Use Tesseract.js for image files
  // 2. Use PDF parsing libraries for PDF files
  // 3. Handle different file formats appropriately
  
  // For now, return a mock text that simulates OCR output
  return `
    INVOICE
    ABC Company Pty Ltd
    ABN: 12 ***********
    
    Invoice #: INV-2024-001
    Date: ${new Date().toISOString().split('T')[0]}
    Due Date: ${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
    
    Description: Professional Services
    Amount: $1,250.00
    GST: $125.00
    Total: $1,375.00
    
    Please pay by due date to avoid late fees.
  `;
}

// Parse invoice text to extract structured data
function parseInvoiceText(text: string) {
  const result = {
    amount: null as number | null,
    due_date: null as string | null,
    vendor: null as string | null,
    category: null as string | null,
    confidence: 0
  };

  try {
    // Extract amount (look for currency patterns)
    const amountMatch = text.match(/(?:Total|Amount|Due)[\s:]*\$?([\d,]+\.?\d*)/i);
    if (amountMatch) {
      result.amount = parseFloat(amountMatch[1].replace(/,/g, ''));
      result.confidence += 0.3;
    }

    // Extract due date
    const dueDateMatch = text.match(/Due\s+Date[\s:]*(\d{4}-\d{2}-\d{2}|\d{1,2}\/\d{1,2}\/\d{4})/i);
    if (dueDateMatch) {
      result.due_date = dueDateMatch[1];
      result.confidence += 0.3;
    }

    // Extract vendor (look for company names or ABN)
    const vendorMatch = text.match(/([A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+Pty\s+Ltd)?)/);
    if (vendorMatch) {
      result.vendor = vendorMatch[1];
      result.confidence += 0.2;
    }

    // Guess category based on keywords
    if (text.toLowerCase().includes('professional services')) {
      result.category = 'Professional Services';
      result.confidence += 0.2;
    } else if (text.toLowerCase().includes('utilities')) {
      result.category = 'Utilities';
      result.confidence += 0.2;
    }

  } catch (error) {
    console.error('Error parsing invoice text:', error);
    result.confidence = 0;
  }

  return result;
} 