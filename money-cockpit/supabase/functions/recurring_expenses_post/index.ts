import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    const today = new Date().toISOString().split('T')[0];

    // Get all recurring expenses due today or earlier
    const { data: dueExpenses, error: fetchError } = await supabase
      .from('recurring_expenses')
      .select('*')
      .lte('next_due_date', today)
      .eq('is_active', true);

    if (fetchError) throw fetchError;

    console.log(`Processing ${dueExpenses?.length || 0} due recurring expenses`);

    let processedCount = 0;
    let errorCount = 0;

    for (const expense of dueExpenses || []) {
      try {
        // Create transaction for the expense
        const { error: txnError } = await supabase
          .from('transactions')
          .insert({
            user_id: expense.user_id,
            entity_id: expense.entity_id,
            type: 'expense',
            amount: expense.amount,
            description: expense.name,
            category_id: expense.category_id,
            transaction_date: expense.next_due_date
          });

        if (txnError) {
          console.error(`Error creating transaction for expense ${expense.id}:`, txnError);
          errorCount++;
          continue;
        }

        // Calculate next due date based on frequency
        let nextDue = new Date(expense.next_due_date);
        
        switch (expense.frequency) {
          case 'weekly':
            nextDue.setDate(nextDue.getDate() + 7);
            break;
          case 'fortnightly':
            nextDue.setDate(nextDue.getDate() + 14);
            break;
          case 'monthly':
            nextDue.setMonth(nextDue.getMonth() + 1);
            break;
          case 'quarterly':
            nextDue.setMonth(nextDue.getMonth() + 3);
            break;
          case 'annually':
            nextDue.setFullYear(nextDue.getFullYear() + 1);
            break;
        }

        // Update next due date
        const { error: updateError } = await supabase
          .from('recurring_expenses')
          .update({ 
            next_due_date: nextDue.toISOString().split('T')[0],
            last_posted_date: expense.next_due_date
          })
          .eq('id', expense.id);

        if (updateError) {
          console.error(`Error updating next due date for expense ${expense.id}:`, updateError);
          errorCount++;
        } else {
          processedCount++;
          console.log(`Posted expense ${expense.name} and updated next due date to ${nextDue.toISOString().split('T')[0]}`);
        }
      } catch (error) {
        console.error(`Error processing expense ${expense.id}:`, error);
        errorCount++;
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Processed ${processedCount} recurring expenses successfully, ${errorCount} errors`,
        processed: processedCount,
        errors: errorCount
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );
  } catch (error) {
    console.error('Error in recurring_expenses_post:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    );
  }
}); 