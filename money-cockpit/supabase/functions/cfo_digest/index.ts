import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get all active users
    const { data: users, error: usersError } = await supabaseClient
      .from('entities')
      .select('owner_id')
      .eq('is_active', true)
      .limit(1000)

    if (usersError) throw usersError

    const uniqueOwnerIds = [...new Set(users?.map(u => u.owner_id) || [])]

    for (const ownerId of uniqueOwnerIds) {
      // Process each lens type
      const lenses = ['personal', 'all']
      
      // Get user's business entities
      const { data: businessEntities } = await supabaseClient
        .from('entities')
        .select('name')
        .eq('owner_id', ownerId)
        .eq('type', 'business')
        .eq('is_active', true)

      if (businessEntities) {
        lenses.push(...businessEntities.map(e => e.name))
      }

      for (const lens of lenses) {
        // Get dashboard data
        const { data: dashboardData } = await supabaseClient
          .rpc('get_dashboard', { p_lens: lens })

        // Get upcoming liabilities
        const { data: upcomingExpenses } = await supabaseClient
          .from('recurring_expenses')
          .select('name, amount, next_due')
          .gte('next_due', new Date().toISOString())
          .lte('next_due', new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString())
          .order('next_due')
          .limit(10)

        // Get recent large transactions
        const { data: recentTransactions } = await supabaseClient
          .from('transactions')
          .select('amount, category, date')
          .gte('date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
          .order('amount', { ascending: false })
          .limit(5)

        // Prepare context for GPT
        const context = {
          lens,
          netWorth: dashboardData?.net_worth || 0,
          totalAssets: dashboardData?.total_assets || 0,
          totalDebt: dashboardData?.total_debt || 0,
          upcomingPayments: dashboardData?.upcoming_payments || 0,
          upcomingExpenses: upcomingExpenses || [],
          recentLargeTransactions: recentTransactions || []
        }

        // Call OpenAI
        const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'gpt-4o-mini',
            messages: [
              {
                role: 'system',
                content: `You are a CFO advisor for an ADHD-friendly finance app. Generate a brief, actionable insight based on the financial data provided. Focus on:
                1. Immediate actions needed (if any)
                2. Positive reinforcement for good financial habits
                3. Simple, clear language
                4. One key metric or trend to focus on
                
                Format your response as JSON with:
                - headline: A short, attention-grabbing headline (max 10 words)
                - body: 2-3 sentences with the key insight and action (if needed)
                - severity: "info", "warning", or "success"`
              },
              {
                role: 'user',
                content: JSON.stringify(context)
              }
            ],
            temperature: 0.7,
            max_tokens: 200
          }),
        })

        if (!openAIResponse.ok) {
          throw new Error(`OpenAI API error: ${openAIResponse.statusText}`)
        }

        const aiData = await openAIResponse.json()
        const insight = JSON.parse(aiData.choices[0].message.content)

        // Insert insight
        const { error: insertError } = await supabaseClient
          .from('agent_insights')
          .insert({
            lens,
            headline: insight.headline,
            body: insight.body,
            severity: insight.severity
          })

        if (insertError) throw insertError
      }
    }

    return new Response(
      JSON.stringify({ success: true, message: 'CFO digest completed' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
}) 