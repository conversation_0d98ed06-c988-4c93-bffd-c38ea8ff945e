import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Get all unpaid invoices
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices_uploaded')
      .select('*')
      .eq('status', 'UNPAID');

    if (invoicesError) throw invoicesError;

    console.log(`Checking ${invoices?.length || 0} unpaid invoices for matches`);

    let matchedCount = 0;

    // Check each unpaid invoice for matching transactions
    for (const invoice of invoices || []) {
      if (!invoice.amount || !invoice.due_date) continue;

      // Calculate date range (5 days before and after due date)
      const dueDate = new Date(invoice.due_date);
      const startDate = new Date(dueDate);
      startDate.setDate(startDate.getDate() - 5);
      const endDate = new Date(dueDate);
      endDate.setDate(endDate.getDate() + 5);

      // Look for matching transactions
      const { data: transactions, error: txnError } = await supabase
        .from('transactions')
        .select('*')
        .eq('entity_id', invoice.entity_id)
        .gte('date', startDate.toISOString().split('T')[0])
        .lte('date', endDate.toISOString().split('T')[0])
        .is('debt_id', null); // Don't match debt payments

      if (txnError) {
        console.error(`Error fetching transactions for invoice ${invoice.id}:`, txnError);
        continue;
      }

      // Find best match (amount within $2 variance)
      const matchingTxn = transactions?.find(txn => {
        const amountDiff = Math.abs(Math.abs(txn.amount) - invoice.amount);
        return amountDiff <= 2; // Allow $2 variance
      });

      if (matchingTxn) {
        // Mark invoice as paid
        const { error: updateError } = await supabase
          .from('invoices_uploaded')
          .update({
            status: 'PAID',
            paid_txn_id: matchingTxn.id,
            updated_at: new Date().toISOString()
          })
          .eq('id', invoice.id);

        if (updateError) {
          console.error(`Error updating invoice ${invoice.id}:`, updateError);
        } else {
          matchedCount++;
          console.log(`Matched invoice ${invoice.id} (${invoice.vendor}, $${invoice.amount}) to transaction ${matchingTxn.id}`);
        }
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Processed ${invoices?.length || 0} invoices, matched ${matchedCount}` 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );
  } catch (error) {
    console.error('Error in invoice_matcher:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    );
  }
}); 