'use client'

import { Fragment, useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { createBrowserClient } from '@/lib/supabase-client'
import { X, TrendingUp } from 'lucide-react'

interface IncomeStreamModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

interface FormData {
  entity_id: string
  name: string
  counterparty: string
  stream_type: 'rev_share' | 'subscription' | 'wage' | 'dividend'
  ownership_percentage: number
  data_source?: 'stripe' | 'manual'
  start_date?: string
  end_date?: string
}

const STREAM_TYPES = [
  { value: 'rev_share', label: 'REV SHARE', color: 'green' },
  { value: 'subscription', label: 'SUBSCRIPTION', color: 'blue' },
  { value: 'wage', label: 'WAGE', color: 'yellow' },
  { value: 'dividend', label: 'DIVIDEND', color: 'purple' },
]

const DATA_SOURCES = [
  { value: 'stripe', label: 'STRIPE' },
  { value: 'manual', label: 'MANUAL' },
]

export default function IncomeStreamModal({ isOpen, onClose, onSuccess }: IncomeStreamModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [entities, setEntities] = useState<Array<{id: string, name: string}>>([])
  
  const supabase = createBrowserClient()
  
  const { register, handleSubmit, reset, watch, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      stream_type: 'subscription',
      ownership_percentage: 100,
      data_source: 'manual'
    }
  })

  const streamType = watch('stream_type')
  const watchedDataSource = watch('data_source')

  useEffect(() => {
    if (isOpen) {
      fetchEntities()
    }
  }, [isOpen])

  const fetchEntities = async () => {
    const { data } = await supabase
      .from('entities')
      .select('*')
      .eq('is_active', true)
      .order('name')
    
    setEntities(data || [])
  }

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      const { error } = await supabase
        .from('income_streams')
        .insert({
          user_id: user.id,
          entity_id: data.entity_id,
          name: data.name,
          counterparty: data.counterparty,
          stream_type: data.stream_type,
          ownership_percentage: data.ownership_percentage / 100, // Convert to decimal
          data_source: data.data_source || 'manual',
          start_date: data.start_date || null,
          end_date: data.end_date || null,
          is_active: true
        })

      if (error) throw error

      reset()
      onSuccess?.()
      onClose()
    } catch (error) {
      console.error('Error creating income stream:', error)
      alert(error instanceof Error ? error.message : 'Failed to create income stream')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden bg-black border-4 border-green-500 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title className="text-green-500 font-mono text-lg font-bold flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    INCOME STREAM
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  {/* Receiving Entity */}
                  <div>
                    <label className="text-green-500 font-mono text-xs mb-2 block">
                      RECEIVING ENTITY *
                    </label>
                    <select
                      {...register('entity_id', { required: 'Entity is required' })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-green-500 focus:outline-none"
                    >
                      <option value="">Select entity...</option>
                      {entities.map((entity) => (
                        <option key={entity.id} value={entity.id}>
                          {entity.name}
                        </option>
                      ))}
                    </select>
                    {errors.entity_id && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.entity_id.message}</p>
                    )}
                  </div>

                  {/* Name */}
                  <div>
                    <label className="text-green-500 font-mono text-xs mb-2 block">
                      NAME *
                    </label>
                    <input
                      type="text"
                      {...register('name', { required: 'Name is required' })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-green-500 focus:outline-none"
                      placeholder="e.g. Monthly Retainer"
                    />
                    {errors.name && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.name.message}</p>
                    )}
                  </div>

                  {/* Counterparty */}
                  <div>
                    <label className="text-green-500 font-mono text-xs mb-2 block">
                      COUNTERPARTY *
                    </label>
                    <input
                      type="text"
                      {...register('counterparty', { required: 'Counterparty is required' })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-green-500 focus:outline-none"
                      placeholder="e.g. Acme Corp"
                    />
                    {errors.counterparty && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.counterparty.message}</p>
                    )}
                  </div>

                  {/* Stream Type */}
                  <div>
                    <label className="text-green-500 font-mono text-xs mb-2 block">
                      STREAM TYPE *
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {STREAM_TYPES.map((type) => (
                        <label
                          key={type.value}
                          className={`
                            relative cursor-pointer border-2 p-3 text-center font-mono text-xs
                            transition-all hover:scale-105
                            ${streamType === type.value
                              ? `border-${type.color}-500 bg-${type.color}-500/20 text-${type.color}-400`
                              : 'border-gray-600 text-gray-400 hover:border-gray-500'
                            }
                          `}
                        >
                          <input
                            type="radio"
                            {...register('stream_type')}
                            value={type.value}
                            className="sr-only"
                          />
                          {type.label}
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Ownership Percentage */}
                  <div>
                    <label className="text-green-500 font-mono text-xs mb-2 block">
                      OWNERSHIP % *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      {...register('ownership_percentage', { 
                        required: 'Ownership percentage is required',
                        min: { value: 0.01, message: 'Must be greater than 0%' },
                        max: { value: 100, message: 'Cannot exceed 100%' }
                      })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-green-500 focus:outline-none"
                      placeholder="100"
                    />
                    {errors.ownership_percentage && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.ownership_percentage.message}</p>
                    )}
                  </div>

                  {/* Data Source */}
                  <div>
                    <label className="text-green-500 font-mono text-xs mb-2 block">
                      DATA SOURCE
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {DATA_SOURCES.map((source) => (
                        <label
                          key={source.value}
                          className={`
                            relative cursor-pointer border-2 p-3 text-center font-mono text-xs
                            transition-all hover:scale-105
                            ${watchedDataSource === source.value
                              ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                              : 'border-gray-600 text-gray-400 hover:border-gray-500'
                            }
                          `}
                        >
                          <input
                            type="radio"
                            {...register('data_source')}
                            value={source.value}
                            className="sr-only"
                          />
                          {source.label}
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Start Date */}
                  <div>
                    <label className="text-green-500 font-mono text-xs mb-2 block">
                      START DATE
                    </label>
                    <input
                      type="date"
                      {...register('start_date')}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-green-500 focus:outline-none"
                    />
                  </div>

                  {/* End Date */}
                  <div>
                    <label className="text-green-500 font-mono text-xs mb-2 block">
                      END DATE
                    </label>
                    <input
                      type="date"
                      {...register('end_date')}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-green-500 focus:outline-none"
                    />
                  </div>

                  {/* Submit Button */}
                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="flex-1 bg-gray-800 border-2 border-gray-600 text-gray-400 font-mono py-3 hover:bg-gray-700 hover:border-gray-500 transition-all"
                    >
                      CANCEL
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`
                        flex-1 border-2 font-mono py-3 transition-all
                        ${isSubmitting
                          ? 'bg-gray-800 border-gray-600 text-gray-500 cursor-not-allowed'
                          : 'bg-green-500 border-green-400 text-black hover:scale-105'
                        }
                      `}
                    >
                      {isSubmitting ? 'CREATING...' : 'CREATE STREAM'}
                    </button>
                  </div>
                </form>

                {/* Pixel corners */}
                <div className="absolute top-0 left-0 w-2 h-2 bg-green-500" />
                <div className="absolute top-0 right-0 w-2 h-2 bg-green-500" />
                <div className="absolute bottom-0 left-0 w-2 h-2 bg-green-500" />
                <div className="absolute bottom-0 right-0 w-2 h-2 bg-green-500" />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 