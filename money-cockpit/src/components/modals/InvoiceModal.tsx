'use client'

import { Fragment, useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { createBrowserClient } from '@/lib/supabase-client'
import { X, FileText, Upload } from 'lucide-react'

interface InvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

interface FormData {
  entity_id: string
  client_name: string
  amount: number
  includes_gst: boolean
  due_date: string
  invoice_number?: string
  description?: string
}

export default function InvoiceModal({ isOpen, onClose, onSuccess }: InvoiceModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [entities, setEntities] = useState<Array<{id: string, name: string}>>([])
  const [pdfFile, setPdfFile] = useState<File | null>(null)
  
  const supabase = createBrowserClient()
  
  const { register, handleSubmit, reset, watch, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      includes_gst: false,
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
    }
  })

  const includesGst = watch('includes_gst')
  const amount = watch('amount')

  useEffect(() => {
    if (isOpen) {
      fetchEntities()
    }
  }, [isOpen])

  const fetchEntities = async () => {
    const { data } = await supabase
      .from('entities')
      .select('*')
      .eq('is_active', true)
      .order('name')
    
    setEntities(data || [])
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && file.type === 'application/pdf') {
      setPdfFile(file)
    } else {
      alert('Please select a PDF file')
    }
  }

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      let pdfUrl = null

      // Upload PDF if provided
      if (pdfFile) {
        const fileName = `${user.id}/${Date.now()}_${pdfFile.name}`
        const { error } = await supabase.storage
          .from('invoices')
          .upload(fileName, pdfFile)

        if (error) throw error
        
        const { data: { publicUrl } } = supabase.storage
          .from('invoices')
          .getPublicUrl(fileName)
        
        pdfUrl = publicUrl
      }

      // Calculate GST amount if applicable
      const gstAmount = includesGst ? amount * 0.1 : 0
      const netAmount = includesGst ? amount / 1.1 : amount

      // Create invoice
      const { error } = await supabase
        .from('invoices')
        .insert({
          user_id: user.id,
          entity_id: data.entity_id,
          client_name: data.client_name,
          invoice_number: data.invoice_number || `INV-${Date.now()}`,
          amount: amount,
          gst_amount: gstAmount,
          net_amount: netAmount,
          due_date: data.due_date,
          description: data.description || null,
          pdf_url: pdfUrl,
          status: 'OPEN',
          issued_date: new Date().toISOString()
        })

      if (error) throw error

      reset()
      setPdfFile(null)
      onSuccess?.()
      onClose()
    } catch (error) {
      console.error('Error creating invoice:', error)
      alert(error instanceof Error ? error.message : 'Failed to create invoice')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden bg-black border-4 border-cyan-500 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title className="text-cyan-500 font-mono text-lg font-bold flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    MANUAL INVOICE
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  {/* Entity */}
                  <div>
                    <label className="text-cyan-500 font-mono text-xs mb-2 block">
                      ENTITY *
                    </label>
                    <select
                      {...register('entity_id', { required: 'Entity is required' })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
                    >
                      <option value="">Select entity...</option>
                      {entities.map((entity) => (
                        <option key={entity.id} value={entity.id}>
                          {entity.name}
                        </option>
                      ))}
                    </select>
                    {errors.entity_id && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.entity_id.message}</p>
                    )}
                  </div>

                  {/* Client */}
                  <div>
                    <label className="text-cyan-500 font-mono text-xs mb-2 block">
                      CLIENT *
                    </label>
                    <input
                      type="text"
                      {...register('client_name', { required: 'Client is required' })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
                      placeholder="e.g. Acme Corp"
                    />
                    {errors.client_name && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.client_name.message}</p>
                    )}
                  </div>

                  {/* Amount */}
                  <div>
                    <label className="text-cyan-500 font-mono text-xs mb-2 block">
                      AMOUNT ($) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      {...register('amount', { 
                        required: 'Amount is required',
                        min: { value: 0.01, message: 'Amount must be greater than 0' }
                      })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
                      placeholder="0.00"
                    />
                    {errors.amount && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.amount.message}</p>
                    )}
                  </div>

                  {/* GST Toggle */}
                  <div>
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        {...register('includes_gst')}
                        className="w-5 h-5 bg-gray-900 border-2 border-gray-600 text-cyan-500 focus:ring-0 focus:ring-offset-0"
                      />
                      <span className="text-cyan-500 font-mono text-xs">
                        INCLUDES GST (10%)
                      </span>
                    </label>
                    {includesGst && amount > 0 && (
                      <div className="mt-2 p-2 bg-cyan-900/20 border border-cyan-600">
                        <p className="text-cyan-400 font-mono text-xs">
                          NET: ${(amount / 1.1).toFixed(2)} + GST: ${(amount * 0.1 / 1.1).toFixed(2)}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Due Date */}
                  <div>
                    <label className="text-cyan-500 font-mono text-xs mb-2 block">
                      DUE DATE *
                    </label>
                    <input
                      type="date"
                      {...register('due_date', { required: 'Due date is required' })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
                    />
                    {errors.due_date && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.due_date.message}</p>
                    )}
                  </div>

                  {/* Invoice Number (Optional) */}
                  <div>
                    <label className="text-cyan-500 font-mono text-xs mb-2 block">
                      INVOICE NUMBER
                    </label>
                    <input
                      type="text"
                      {...register('invoice_number')}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
                      placeholder="Auto-generated if empty"
                    />
                  </div>

                  {/* Description (Optional) */}
                  <div>
                    <label className="text-cyan-500 font-mono text-xs mb-2 block">
                      DESCRIPTION
                    </label>
                    <textarea
                      {...register('description')}
                      rows={3}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none resize-none"
                      placeholder="What's this invoice for?"
                    />
                  </div>

                  {/* PDF Upload */}
                  <div>
                    <label className="text-cyan-500 font-mono text-xs mb-2 block">
                      PDF UPLOAD
                    </label>
                    <div className="relative">
                      <input
                        type="file"
                        accept=".pdf"
                        onChange={handleFileChange}
                        className="sr-only"
                        id="pdf-upload"
                      />
                      <label
                        htmlFor="pdf-upload"
                        className="flex items-center gap-3 w-full bg-gray-900 border-2 border-gray-600 text-gray-400 font-mono px-4 py-3 cursor-pointer hover:border-cyan-500 hover:text-cyan-400 transition-all"
                      >
                        <Upload className="h-5 w-5" />
                        {pdfFile ? pdfFile.name : 'Choose PDF file...'}
                      </label>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="flex-1 bg-gray-800 border-2 border-gray-600 text-gray-400 font-mono py-3 hover:bg-gray-700 hover:border-gray-500 transition-all"
                    >
                      CANCEL
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`
                        flex-1 border-2 font-mono py-3 transition-all
                        ${isSubmitting
                          ? 'bg-gray-800 border-gray-600 text-gray-500 cursor-not-allowed'
                          : 'bg-cyan-500 border-cyan-400 text-black hover:scale-105'
                        }
                      `}
                    >
                      {isSubmitting ? 'CREATING...' : 'CREATE INVOICE'}
                    </button>
                  </div>
                </form>

                {/* Pixel corners */}
                <div className="absolute top-0 left-0 w-2 h-2 bg-cyan-500" />
                <div className="absolute top-0 right-0 w-2 h-2 bg-cyan-500" />
                <div className="absolute bottom-0 left-0 w-2 h-2 bg-cyan-500" />
                <div className="absolute bottom-0 right-0 w-2 h-2 bg-cyan-500" />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 