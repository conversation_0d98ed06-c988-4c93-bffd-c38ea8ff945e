'use client'

import { Fragment, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { createBrowserClient } from '@/lib/supabase-client'
import { X, Tag } from 'lucide-react'

interface AddCategoryModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

interface FormData {
  name: string
  scope: 'personal' | 'business' | 'both'
  gst_default: boolean
}

export default function AddCategoryModal({ isOpen, onClose, onSuccess }: AddCategoryModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const supabase = createBrowserClient()
  
  const { register, handleSubmit, reset, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      scope: 'both',
      gst_default: false
    }
  })

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      // Check for duplicate name
      const { data: existing } = await supabase
        .from('categories')
        .select('id')
        .eq('name', data.name)
        .eq('is_active', true)
        .single()

      if (existing) {
        throw new Error('A category with this name already exists')
      }

      // Create category
      const { error } = await supabase
        .from('categories')
        .insert({
          user_id: user.id,
          name: data.name,
          scope: data.scope,
          gst_default: data.gst_default,
          is_active: true
        })

      if (error) throw error

      reset()
      onSuccess?.()
      onClose()
    } catch (error) {
      console.error('Error creating category:', error)
      alert(error instanceof Error ? error.message : 'Failed to create category')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden bg-black border-4 border-cyan-500 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title className="text-cyan-500 font-mono text-lg font-bold flex items-center gap-2">
                    <Tag className="h-5 w-5" />
                    NEW CATEGORY
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  {/* Name */}
                  <div>
                    <label className="text-cyan-500 font-mono text-xs mb-2 block">
                      NAME *
                    </label>
                    <input
                      type="text"
                      {...register('name', { 
                        required: 'Name is required',
                        minLength: { value: 2, message: 'Name must be at least 2 characters' }
                      })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
                      placeholder="e.g. Groceries"
                    />
                    {errors.name && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.name.message}</p>
                    )}
                  </div>

                  {/* Scope */}
                  <div>
                    <label className="text-cyan-500 font-mono text-xs mb-2 block">
                      SCOPE *
                    </label>
                    <select
                      {...register('scope', { required: 'Scope is required' })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
                    >
                      <option value="personal">Personal Only</option>
                      <option value="business">Business Only</option>
                      <option value="both">Both</option>
                    </select>
                    {errors.scope && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.scope.message}</p>
                    )}
                  </div>

                  {/* GST Default */}
                  <div>
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        {...register('gst_default')}
                        className="w-5 h-5 bg-gray-900 border-2 border-gray-600 text-cyan-500 focus:ring-0 focus:ring-offset-0"
                      />
                      <span className="text-cyan-500 font-mono text-xs">
                        GST DEFAULT
                      </span>
                    </label>
                    <p className="text-gray-400 font-mono text-xs mt-1 ml-8">
                      Transactions in this category will default to including GST
                    </p>
                  </div>

                  {/* Submit Button */}
                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="flex-1 bg-gray-800 border-2 border-gray-600 text-gray-400 font-mono py-3 hover:bg-gray-700 hover:border-gray-500 transition-all"
                    >
                      CANCEL
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`
                        flex-1 border-2 font-mono py-3 transition-all
                        ${isSubmitting
                          ? 'bg-gray-800 border-gray-600 text-gray-500 cursor-not-allowed'
                          : 'bg-cyan-500 border-cyan-400 text-black hover:scale-105'
                        }
                      `}
                    >
                      {isSubmitting ? 'CREATING...' : 'CREATE CATEGORY'}
                    </button>
                  </div>
                </form>

                {/* Pixel corners */}
                <div className="absolute top-0 left-0 w-2 h-2 bg-cyan-500" />
                <div className="absolute top-0 right-0 w-2 h-2 bg-cyan-500" />
                <div className="absolute bottom-0 left-0 w-2 h-2 bg-cyan-500" />
                <div className="absolute bottom-0 right-0 w-2 h-2 bg-cyan-500" />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 