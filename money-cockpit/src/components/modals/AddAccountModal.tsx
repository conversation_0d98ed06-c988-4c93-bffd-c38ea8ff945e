'use client'

import { Fragment, useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { createBrowserClient } from '@/lib/supabase-client'
import { useWorkspace } from '@/lib/contexts/WorkspaceContext'
import { X, CreditCard } from 'lucide-react'
import OwnerSelector from '@/components/forms/OwnerSelector'

interface AddAccountModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

interface FormData {
  entity_id: string
  provider: string
  currency: string
  display_name?: string
  api_key?: string
  owner_selection?: string
}



const CURRENCIES = [
  { code: 'AUD', name: 'Australian Dollar' },
  { code: 'USD', name: 'US Dollar' },
  { code: 'EUR', name: 'Euro' },
  { code: 'GBP', name: 'British Pound' },
  { code: 'NZD', name: 'New Zealand Dollar' },
  { code: 'CAD', name: 'Canadian Dollar' },
  { code: '<PERSON><PERSON>', name: 'Japanese Yen' },
  { code: 'CN<PERSON>', name: 'Chinese Yuan' },
]

export default function AddAccountModal({ isOpen, onClose, onSuccess }: AddAccountModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [entities, setEntities] = useState<Array<{id: string, name: string, type?: string}>>([])
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  
  const supabase = createBrowserClient()
  const { currentWorkspaceId } = useWorkspace()
  
  const { register, handleSubmit, watch, reset, setValue, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      currency: 'AUD'
    }
  })

  const selectedEntityId = watch('entity_id')
  const selectedEntity = entities.find(e => e.id === selectedEntityId)
  const isPersonalEntity = selectedEntity?.type === 'personal'

  useEffect(() => {
    if (isOpen && currentWorkspaceId) {
      fetchEntities()
      fetchCurrentUser()
    }
  }, [isOpen, currentWorkspaceId])

  const fetchCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setCurrentUserId(user?.id || null)
  }

  const fetchEntities = async () => {
    if (!currentWorkspaceId) return

    const { data } = await supabase
      .from('entities')
      .select('*')
      .eq('workspace_id', currentWorkspaceId)
      .eq('is_active', true)
      .order('name')
    
    // Add Personal as a virtual entity
    const allEntities = [
      { id: 'personal', name: 'Personal', type: 'personal' },
      ...(data || [])
    ]
    
    setEntities(allEntities)
  }

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      // Check for duplicate account
      const { data: existing } = await supabase
        .from('accounts')
        .select('id')
        .eq('entity_id', data.entity_id)
        .eq('display_name', data.display_name || data.provider)
        .single()

      if (existing) {
        throw new Error('An account with this name already exists for this entity')
      }

      // Determine owner attribution for personal entities
      const isShared = !data.owner_selection || data.owner_selection === 'shared'
      const ownerUserId = isPersonalEntity && !isShared ? data.owner_selection : null

      // Create account
      const { error } = await supabase
        .from('accounts')
        .insert({
          entity_id: data.entity_id,
          provider: data.provider,
          currency: data.currency,
          display_name: data.display_name || data.provider,
          api_key: data.api_key || null,
          is_shared: isPersonalEntity ? isShared : true,
          owner_user_id: ownerUserId
        })

      if (error) throw error

      reset()
      onSuccess?.()
      onClose()
    } catch (error) {
      console.error('Error creating account:', error)
      alert(error instanceof Error ? error.message : 'Failed to create account')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden bg-black border-4 border-blue-500 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title className="text-blue-500 font-mono text-lg font-bold flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    ADD ACCOUNT
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  {/* Entity */}
                  <div>
                    <label className="text-blue-500 font-mono text-xs mb-2 block">
                      ENTITY *
                    </label>
                    <select
                      {...register('entity_id', { required: 'Entity is required' })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-blue-500 focus:outline-none"
                    >
                      <option value="">Select entity...</option>
                      {entities.map((entity) => (
                        <option key={entity.id} value={entity.id}>
                          {entity.name}
                        </option>
                      ))}
                    </select>
                    {errors.entity_id && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.entity_id.message}</p>
                    )}
                  </div>

                  {/* Provider */}
                  <div>
                    <label className="text-blue-500 font-mono text-xs mb-2 block">
                      PROVIDER *
                    </label>
                    <input
                      type="text"
                      {...register('provider', { 
                        required: 'Provider is required',
                        minLength: { value: 2, message: 'Provider must be at least 2 characters' }
                      })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-blue-500 focus:outline-none"
                      placeholder="e.g. Commonwealth Bank"
                    />
                    {errors.provider && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.provider.message}</p>
                    )}
                  </div>

                  {/* Currency */}
                  <div>
                    <label className="text-blue-500 font-mono text-xs mb-2 block">
                      CURRENCY *
                    </label>
                    <select
                      {...register('currency', { required: 'Currency is required' })}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-blue-500 focus:outline-none"
                    >
                      {CURRENCIES.map((currency) => (
                        <option key={currency.code} value={currency.code}>
                          {currency.code} - {currency.name}
                        </option>
                      ))}
                    </select>
                    {errors.currency && (
                      <p className="text-red-400 font-mono text-xs mt-1">{errors.currency.message}</p>
                    )}
                  </div>

                  {/* Display Name (Optional) */}
                  <div>
                    <label className="text-blue-500 font-mono text-xs mb-2 block">
                      CARD/WALLET TAG
                    </label>
                    <input
                      type="text"
                      {...register('display_name')}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-blue-500 focus:outline-none"
                      placeholder="e.g. Business Debit"
                    />
                  </div>

                  {/* Owner (only for personal entities) */}
                  {isPersonalEntity && (
                    <div>
                      <label className="text-blue-500 font-mono text-xs mb-2 block">
                        OWNER
                      </label>
                      <OwnerSelector
                        value={watch('owner_selection')}
                        onChange={(value) => setValue('owner_selection', value)}
                        currentUserId={currentUserId || undefined}
                      />
                    </div>
                  )}

                  {/* API Key (Optional) */}
                  <div>
                    <label className="text-blue-500 font-mono text-xs mb-2 block">
                      API KEY
                    </label>
                    <input
                      type="password"
                      {...register('api_key')}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-blue-500 focus:outline-none"
                      placeholder="For automated imports"
                    />
                  </div>

                  {/* Submit Button */}
                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="flex-1 bg-gray-800 border-2 border-gray-600 text-gray-400 font-mono py-3 hover:bg-gray-700 hover:border-gray-500 transition-all"
                    >
                      CANCEL
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`
                        flex-1 border-2 font-mono py-3 transition-all
                        ${isSubmitting
                          ? 'bg-gray-800 border-gray-600 text-gray-500 cursor-not-allowed'
                          : 'bg-blue-500 border-blue-400 text-black hover:scale-105'
                        }
                      `}
                    >
                      {isSubmitting ? 'CREATING...' : 'CREATE ACCOUNT'}
                    </button>
                  </div>
                </form>

                {/* Pixel corners */}
                <div className="absolute top-0 left-0 w-2 h-2 bg-blue-500" />
                <div className="absolute top-0 right-0 w-2 h-2 bg-blue-500" />
                <div className="absolute bottom-0 left-0 w-2 h-2 bg-blue-500" />
                <div className="absolute bottom-0 right-0 w-2 h-2 bg-blue-500" />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 