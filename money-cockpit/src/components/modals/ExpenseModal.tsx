'use client'

import { Fragment, useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { createBrowserClient } from '@/lib/supabase-client'
import { useWorkspace } from '@/lib/contexts/WorkspaceContext'
import { useCategories } from '@/lib/hooks/useCategories'
import { X, DollarSign } from 'lucide-react'
import OwnerSelector from '@/components/forms/OwnerSelector'

interface ExpenseModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

interface FormData {
  entity_id: string
  account_id: string
  amount: number
  category_id: string
  bucket?: string
  memo?: string
  date: string
  owner_selection?: string
}

interface Entity {
  id: string
  name: string
  type: 'personal' | 'business' | 'trust'
}

interface Account {
  id: string
  name: string
  entity_id: string
}

const PERSONAL_BUCKETS = ['Daily', 'Splurge', 'Smile', 'Fire']

export default function ExpenseModal({ isOpen, onClose, onSuccess }: ExpenseModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [entities, setEntities] = useState<Entity[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  
  const supabase = createBrowserClient()
  const { currentWorkspaceId } = useWorkspace()
  const { categories } = useCategories()
  
  const { register, handleSubmit, watch, reset, setValue, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      date: new Date().toISOString().split('T')[0]
    }
  })

  const selectedEntityId = watch('entity_id')
  const selectedEntity = entities.find(e => e.id === selectedEntityId)
  const isPersonalEntity = selectedEntity?.type === 'personal'

  useEffect(() => {
    if (isOpen && currentWorkspaceId) {
      fetchEntities()
      fetchCurrentUser()
      reset({
        date: new Date().toISOString().split('T')[0]
      })
    }
  }, [isOpen, currentWorkspaceId, reset])

  const fetchCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setCurrentUserId(user?.id || null)
  }

  useEffect(() => {
    if (selectedEntityId) {
      fetchAccounts(selectedEntityId)
      // Clear bucket if switching to non-personal entity
      if (!isPersonalEntity) {
        setValue('bucket', '')
      }
    }
  }, [selectedEntityId, isPersonalEntity, setValue])

  const fetchEntities = async () => {
    if (!currentWorkspaceId) return
    
    const { data, error } = await supabase
      .from('entities')
      .select('*')
      .eq('workspace_id', currentWorkspaceId)
      .eq('is_active', true)
      .order('name')
    
    if (!error) {
      // Add Personal as a virtual entity
      const allEntities = [
        { id: 'personal', name: 'Personal', type: 'personal' as const },
        ...(data || [])
      ]
      setEntities(allEntities)
    }
  }

  const fetchAccounts = async (entityId: string) => {
    const { data, error } = await supabase
      .from('accounts')
      .select('*')
      .eq('entity_id', entityId)
      .order('name')
    
    if (!error && data) {
      setAccounts(data)
    }
  }

  const onSubmit = async (data: FormData) => {
    if (!currentWorkspaceId) return
    
    setIsSubmitting(true)
    
    try {
      // Determine owner attribution for personal entities
      const isShared = !data.owner_selection || data.owner_selection === 'shared'
      const ownerUserId = isPersonalEntity && !isShared ? data.owner_selection : null

      // Insert transaction with negative amount (expense)
      const { error } = await supabase
        .from('transactions')
        .insert({
          account_id: data.account_id,
          amount: -Math.abs(data.amount), // Make it negative
          currency: 'AUD',
          date: data.date,
          category: categories.find(c => c.id === data.category_id)?.name || 'Uncategorized',
          bucket: data.bucket || null,
          memo: data.memo || null,
          is_shared: isPersonalEntity ? isShared : true,
          owner_user_id: ownerUserId
        })

      if (error) throw error

      // Show success toast
      // You can implement a toast system here
      console.log('Expense recorded successfully')
      
      reset()
      onSuccess?.()
      onClose()
    } catch (error) {
      console.error('Error creating expense:', error)
      alert(error instanceof Error ? error.message : 'Failed to create expense')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden bg-black border-2 border-red-500 p-0 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-red-500">
                  <div className="flex items-center gap-3">
                    <DollarSign className="h-6 w-6 text-red-500" />
                    <Dialog.Title className="text-red-500 font-mono text-lg font-bold">
                      NEW EXPENSE
                    </Dialog.Title>
                  </div>
                  <button
                    onClick={onClose}
                    className="text-gray-500 hover:text-white transition-colors"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Entity */}
                    <div>
                      <label className="text-red-500 font-mono text-xs mb-2 block">
                        ENTITY *
                      </label>
                      <select
                        {...register('entity_id', { required: 'Entity is required' })}
                        className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-red-500 focus:outline-none"
                      >
                        <option value="">Select entity...</option>
                        {entities.map((entity) => (
                          <option key={entity.id} value={entity.id}>
                            {entity.name}
                          </option>
                        ))}
                      </select>
                      {errors.entity_id && (
                        <p className="text-red-400 font-mono text-xs mt-1">{errors.entity_id.message}</p>
                      )}
                    </div>

                    {/* Account */}
                    <div>
                      <label className="text-red-500 font-mono text-xs mb-2 block">
                        ACCOUNT *
                      </label>
                      <select
                        {...register('account_id', { required: 'Account is required' })}
                        className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-red-500 focus:outline-none"
                        disabled={!selectedEntityId}
                      >
                        <option value="">Select account...</option>
                        {accounts.map((account) => (
                          <option key={account.id} value={account.id}>
                            {account.name}
                          </option>
                        ))}
                      </select>
                      {errors.account_id && (
                        <p className="text-red-400 font-mono text-xs mt-1">{errors.account_id.message}</p>
                      )}
                    </div>

                    {/* Amount */}
                    <div>
                      <label className="text-red-500 font-mono text-xs mb-2 block">
                        AMOUNT ($) *
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0.01"
                        {...register('amount', { 
                          required: 'Amount is required',
                          min: { value: 0.01, message: 'Amount must be greater than 0' }
                        })}
                        className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-red-500 focus:outline-none"
                        placeholder="0.00"
                      />
                      {errors.amount && (
                        <p className="text-red-400 font-mono text-xs mt-1">{errors.amount.message}</p>
                      )}
                    </div>

                    {/* Category */}
                    <div>
                      <label className="text-red-500 font-mono text-xs mb-2 block">
                        CATEGORY *
                      </label>
                      <select
                        {...register('category_id', { required: 'Category is required' })}
                        className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-red-500 focus:outline-none"
                      >
                        <option value="">Select category...</option>
                        {categories
                          .filter(c => c.scope === 'both' || 
                            (isPersonalEntity && c.scope === 'personal') ||
                            (!isPersonalEntity && c.scope === 'business'))
                          .map((category) => (
                            <option key={category.id} value={category.id}>
                              {category.name}
                            </option>
                          ))}
                      </select>
                      {errors.category_id && (
                        <p className="text-red-400 font-mono text-xs mt-1">{errors.category_id.message}</p>
                      )}
                    </div>

                    {/* Bucket (only for personal entities) */}
                    {isPersonalEntity && (
                      <div>
                        <label className="text-red-500 font-mono text-xs mb-2 block">
                          BUCKET
                        </label>
                        <select
                          {...register('bucket')}
                          className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-red-500 focus:outline-none"
                        >
                          <option value="">Select bucket...</option>
                          {PERSONAL_BUCKETS.map((bucket) => (
                            <option key={bucket} value={bucket}>
                              {bucket}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}

                    {/* Date */}
                    <div>
                      <label className="text-red-500 font-mono text-xs mb-2 block">
                        DATE *
                      </label>
                      <input
                        type="date"
                        {...register('date', { required: 'Date is required' })}
                        className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-red-500 focus:outline-none"
                      />
                      {errors.date && (
                        <p className="text-red-400 font-mono text-xs mt-1">{errors.date.message}</p>
                      )}
                    </div>
                  </div>

                  {/* Owner (only for personal entities) */}
                  {isPersonalEntity && (
                    <div>
                      <label className="text-red-500 font-mono text-xs mb-2 block">
                        OWNER
                      </label>
                      <OwnerSelector
                        value={watch('owner_selection')}
                        onChange={(value) => setValue('owner_selection', value)}
                        currentUserId={currentUserId || undefined}
                      />
                    </div>
                  )}

                  {/* Memo */}
                  <div>
                    <label className="text-red-500 font-mono text-xs mb-2 block">
                      MEMO
                    </label>
                    <textarea
                      {...register('memo')}
                      rows={3}
                      className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-red-500 focus:outline-none resize-none"
                      placeholder="Optional description..."
                    />
                  </div>

                  {/* Submit Button */}
                  <div className="flex gap-4 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="flex-1 border-2 border-gray-600 text-gray-400 font-mono py-3 hover:bg-gray-800 transition-all"
                    >
                      CANCEL
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex-1 border-2 border-red-500 bg-red-500 text-black font-mono py-3 hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? 'RECORDING...' : 'RECORD EXPENSE'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 