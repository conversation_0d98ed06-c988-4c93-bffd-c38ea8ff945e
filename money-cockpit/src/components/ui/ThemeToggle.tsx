'use client'

import { useTheme } from '@/lib/contexts/ThemeContext'
import { Gamepad2, Briefcase } from 'lucide-react'

export default function ThemeToggle() {
  const { theme, toggleTheme } = useTheme()

  return (
    <button
      onClick={toggleTheme}
      className={`
        relative inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-all
        ${theme === 'retro' 
          ? 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700' 
          : 'bg-black border-2 border-cyan-500 text-cyan-500 hover:bg-cyan-500/20 font-mono'
        }
      `}
      title={`Switch to ${theme === 'retro' ? 'Normal' : 'Retro'} theme`}
    >
      {theme === 'retro' ? (
        <>
          <Briefcase className="h-4 w-4" />
          <span className="text-sm">Normal</span>
        </>
      ) : (
        <>
          <Gamepad2 className="h-4 w-4" />
          <span className="text-xs uppercase">Retro</span>
        </>
      )}
    </button>
  )
} 