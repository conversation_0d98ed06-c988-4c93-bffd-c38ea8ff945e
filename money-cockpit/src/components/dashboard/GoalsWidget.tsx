'use client'

import { useEffect, useState } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { useWorkspace } from '@/lib/contexts/WorkspaceContext'
import { Target, TrendingUp } from 'lucide-react'
import Link from 'next/link'

interface Goal {
  id: string
  name: string
  target_amount?: number
  target_date?: string
  status: 'active' | 'hit' | 'paused'
  icon?: string
  entity_id?: string
  entity_name?: string
  current_amount: number
  created_at: string
}

interface GoalsWidgetProps {
  lens: string
}

export default function GoalsWidget({ lens }: GoalsWidgetProps) {
  const [goals, setGoals] = useState<Goal[]>([])
  const [isLoading, setIsLoading] = useState(true)
  
  const supabase = createBrowserClient()
  const { currentWorkspaceId } = useWorkspace()

  useEffect(() => {
    if (currentWorkspaceId) {
      fetchGoals()
    }
  }, [currentWorkspaceId, lens])

  const fetchGoals = async () => {
    if (!currentWorkspaceId) return
    
    setIsLoading(true)
    
    try {
      let query = supabase
        .from('goals')
        .select(`
          *,
          entities(name)
        `)
        .eq('workspace_id', currentWorkspaceId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(3)

      // Filter by lens if not 'all'
      if (lens !== 'all') {
        if (lens === 'personal') {
          query = query.is('entity_id', null)
        } else {
          // Business entity lens
          const { data: entities } = await supabase
            .from('entities')
            .select('id')
            .eq('workspace_id', currentWorkspaceId)
            .eq('name', lens)
            .single()
          
          if (entities) {
            query = query.eq('entity_id', entities.id)
          }
        }
      }

      const { data, error } = await query

      if (error) throw error

      const formattedData = await Promise.all(
        (data || []).map(async (g) => ({
          id: g.id,
          name: g.name,
          target_amount: g.target_amount,
          target_date: g.target_date,
          status: g.status,
          icon: g.icon,
          entity_id: g.entity_id,
          entity_name: g.entities?.name,
          current_amount: await calculateCurrentAmount(g),
          created_at: g.created_at
        }))
      )

      setGoals(formattedData)
    } catch (error) {
      console.error('Error fetching goals:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const calculateCurrentAmount = async (goal: Goal): Promise<number> => {
    if (!goal.entity_id || !goal.target_amount) return 0
    
    try {
      const { data, error } = await supabase
        .from('accounts')
        .select('id')
        .eq('entity_id', goal.entity_id)

      if (error || !data) return 0

      const accountIds = data.map(a => a.id)
      
      const { data: transactions, error: txnError } = await supabase
        .from('transactions')
        .select('amount')
        .in('account_id', accountIds)

      if (txnError || !transactions) return 0

      return transactions.reduce((sum, t) => sum + t.amount, 0)
    } catch (error) {
      console.error('Error calculating current amount:', error)
      return 0
    }
  }

  const calculateProgress = (goal: Goal) => {
    if (goal.target_amount) {
      return Math.min((goal.current_amount / goal.target_amount) * 100, 100)
    }
    
    if (goal.target_date) {
      const now = new Date()
      const target = new Date(goal.target_date)
      const created = new Date(goal.created_at)
      const totalDays = Math.ceil((target.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
      const daysPassed = Math.ceil((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
      return Math.min((daysPassed / totalDays) * 100, 100)
    }
    
    return 0
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 75) return 'bg-green-500'
    if (percentage >= 40) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const getProgressTextColor = (percentage: number) => {
    if (percentage >= 75) return 'text-green-400'
    if (percentage >= 40) return 'text-yellow-400'
    return 'text-red-400'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'short'
    })
  }

  if (isLoading) {
    return (
      <div className="bg-black border-2 border-yellow-500 p-6">
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-16 bg-gray-800 rounded" />
          ))}
        </div>
      </div>
    )
  }

  if (goals.length === 0) {
    return (
      <div className="bg-black border-2 border-yellow-500 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-yellow-500 font-mono text-sm font-bold">GOALS</h3>
          <Target className="h-5 w-5 text-yellow-500" />
        </div>
        <div className="text-center py-8">
          <Target className="mx-auto h-12 w-12 text-gray-600 mb-4" />
          <p className="text-gray-500 font-mono text-sm mb-4">No active goals</p>
          <Link
            href="/goals"
            className="inline-block border-2 border-yellow-500 bg-yellow-500 text-black font-mono px-4 py-2 hover:scale-105 transition-all text-xs"
          >
            CREATE GOAL
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-black border-2 border-yellow-500 p-6 relative">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-yellow-500 font-mono text-sm font-bold">
          GOALS ({lens.toUpperCase()})
        </h3>
        <div className="flex items-center gap-2">
          <TrendingUp className="h-4 w-4 text-yellow-500" />
          <Link
            href="/goals"
            className="text-yellow-400 font-mono text-xs hover:text-yellow-300 transition-colors"
          >
            VIEW ALL →
          </Link>
        </div>
      </div>

      {/* Goals List */}
      <div className="space-y-4">
        {goals.map((goal) => {
          const progress = calculateProgress(goal)
          const progressColor = getProgressColor(progress)
          const textColor = getProgressTextColor(progress)
          
          return (
            <div key={goal.id} className="space-y-2">
              {/* Goal Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{goal.icon || '🎯'}</span>
                  <div>
                    <span className="text-white font-mono text-sm">{goal.name}</span>
                    {goal.entity_name && (
                      <span className="text-gray-400 font-mono text-xs ml-2">
                        ({goal.entity_name})
                      </span>
                    )}
                  </div>
                </div>
                <span className={`font-mono text-xs ${textColor}`}>
                  {progress.toFixed(0)}%
                </span>
              </div>

              {/* Progress Bar */}
              <div className="relative h-4 bg-gray-800 border border-gray-600">
                <div 
                  className={`absolute inset-y-0 left-0 ${progressColor} transition-all duration-500`}
                  style={{ width: `${progress}%` }}
                />
                
                {/* Segments */}
                <div className="absolute inset-0 flex">
                  {[...Array(10)].map((_, i) => (
                    <div key={i} className="flex-1 border-r border-gray-700 last:border-r-0" />
                  ))}
                </div>
              </div>

              {/* Goal Details */}
              <div className="flex justify-between text-xs font-mono">
                {goal.target_amount ? (
                  <>
                    <span className="text-gray-400">
                      {formatCurrency(goal.current_amount)}
                    </span>
                    <span className="text-white">
                      / {formatCurrency(goal.target_amount)}
                    </span>
                  </>
                ) : goal.target_date ? (
                  <>
                    <span className="text-gray-400">Target:</span>
                    <span className="text-white">
                      {formatDate(goal.target_date)}
                    </span>
                  </>
                ) : null}
              </div>
            </div>
          )
        })}
      </div>

      {/* Pixel corners */}
      <div className="absolute top-0 left-0 w-2 h-2 bg-yellow-500" />
      <div className="absolute top-0 right-0 w-2 h-2 bg-yellow-500" />
      <div className="absolute bottom-0 left-0 w-2 h-2 bg-yellow-500" />
      <div className="absolute bottom-0 right-0 w-2 h-2 bg-yellow-500" />
    </div>
  )
} 