'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { AgentInsight } from '@/types/database'
import { AlertCircle, CheckCircle, Info, X } from 'lucide-react'

interface InsightBannerProps {
  lens: string
}

export default function InsightBanner({ lens }: InsightBannerProps) {
  const [insight, setInsight] = useState<AgentInsight | null>(null)
  const [isVisible, setIsVisible] = useState(true)
  const supabase = createBrowserClient()

  useEffect(() => {
    fetchLatestInsight()
  }, [lens])

  const fetchLatestInsight = async () => {
    const { data, error } = await supabase
      .rpc('get_latest_insight', { p_lens: lens })

    if (!error && data) {
      setInsight(data)
      setIsVisible(true)
    }
  }

  if (!insight || !isVisible) return null

  const severityConfig = {
    info: {
      icon: Info,
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800',
      textColor: 'text-blue-800 dark:text-blue-200',
      iconColor: 'text-blue-600 dark:text-blue-400'
    },
    warning: {
      icon: AlertCircle,
      bgColor: 'bg-amber-50 dark:bg-amber-900/20',
      borderColor: 'border-amber-200 dark:border-amber-800',
      textColor: 'text-amber-800 dark:text-amber-200',
      iconColor: 'text-amber-600 dark:text-amber-400'
    },
    success: {
      icon: CheckCircle,
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-800',
      textColor: 'text-green-800 dark:text-green-200',
      iconColor: 'text-green-600 dark:text-green-400'
    }
  }

  const config = severityConfig[insight.severity]
  const Icon = config.icon

  return (
    <div className={`
      ${config.bgColor} ${config.borderColor} ${config.textColor}
      border rounded-lg p-4 mb-6 relative
    `}>
      <div className="flex items-start gap-3">
        <Icon className={`${config.iconColor} h-5 w-5 mt-0.5 flex-shrink-0`} />
        <div className="flex-1">
          <h3 className="font-semibold text-sm mb-1">{insight.headline}</h3>
          <p className="text-sm opacity-90">{insight.body}</p>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  )
} 