'use client'

import { DashboardData } from '@/types/database'
import { retroTheme, getLevelFromNetWorth } from '@/lib/theme'
import { useSpring, animated } from '@react-spring/web'
import { useEffect, useState } from 'react'

interface RetroHeroTilesProps {
  data: DashboardData
}

export default function RetroHeroTiles({ data }: RetroHeroTilesProps) {
  const [mounted, setMounted] = useState(false)
  const level = getLevelFromNetWorth(data.net_worth)
  
  useEffect(() => {
    setMounted(true)
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const debtPercentage = data.total_assets > 0 
    ? (data.total_debt / (data.total_assets + data.total_debt)) * 100
    : 0

  // Animated values
  const netWorthSpring = useSpring({
    from: { value: 0 },
    to: { value: mounted ? data.net_worth : 0 },
    config: { tension: 50, friction: 10 }
  })

  const debtSpring = useSpring({
    from: { width: '0%' },
    to: { width: mounted ? `${Math.min(debtPercentage, 100)}%` : '0%' },
    config: { tension: 50, friction: 10 }
  })

  // Health bar color based on debt percentage
  const getHealthColor = () => {
    if (debtPercentage > 75) return retroTheme.colors.health.critical
    if (debtPercentage > 50) return retroTheme.colors.health.low
    if (debtPercentage > 25) return retroTheme.colors.health.medium
    return retroTheme.colors.health.full
  }

  return (
    <div className="mb-8">
      {/* Player Status Bar */}
      <div className="bg-black border-2 border-green-500 p-4 mb-6 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="h-full w-full bg-gradient-to-b from-transparent via-green-500 to-transparent animate-pulse" />
        </div>
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-4">
              <span className="text-green-500 font-mono text-xs">PLAYER</span>
              <span className="text-white font-mono text-sm">LEVEL {level.level}</span>
              <span className="text-yellow-400 font-mono text-sm">{level.title}</span>
            </div>
            <div className="text-green-500 font-mono text-xs animate-pulse">
              SCORE: {Math.max(0, Math.floor(data.net_worth))}
            </div>
          </div>
          
          {/* XP Bar */}
          <div className="h-2 bg-gray-800 border border-gray-600">
            <div 
              className="h-full bg-gradient-to-r from-green-500 to-yellow-400 transition-all duration-1000"
              style={{ 
                width: `${((data.net_worth - level.minWorth) / 1000) * 100}%`,
                maxWidth: '100%'
              }}
            />
          </div>
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Net Worth */}
        <div className="bg-black border-2 border-green-500 p-6 relative group">
          <div className="absolute inset-0 bg-green-500 opacity-0 group-hover:opacity-10 transition-opacity" />
          
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-2">
              <span className="text-green-500 font-mono text-xs">NET WORTH</span>
              <span className="text-2xl">💰</span>
            </div>
            
            <animated.div className="text-2xl font-mono font-bold">
              <animated.span className={data.net_worth >= 0 ? 'text-green-400' : 'text-red-400'}>
                {netWorthSpring.value.to(val => formatCurrency(val))}
              </animated.span>
            </animated.div>
            
            <div className="mt-2 text-xs font-mono text-gray-400">
              {data.net_worth >= 0 ? '▲ PROFIT' : '▼ LOSS'}
            </div>
          </div>
        </div>

        {/* Debt Health Bar */}
        <div className="bg-black border-2 border-red-500 p-6 relative group">
          <div className="absolute inset-0 bg-red-500 opacity-0 group-hover:opacity-10 transition-opacity" />
          
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-2">
              <span className="text-red-500 font-mono text-xs">DEBT LEVEL</span>
              <span className="text-2xl">⚠️</span>
            </div>
            
            <div className="text-2xl font-mono font-bold text-red-400 mb-3">
              {formatCurrency(data.total_debt)}
            </div>
            
            {/* Health Bar */}
            <div className="space-y-1">
              <div className="flex justify-between text-xs font-mono text-gray-400">
                <span>HEALTH</span>
                <span>{Math.round(100 - debtPercentage)}%</span>
              </div>
              <div className="h-4 bg-gray-800 border border-gray-600 relative">
                <animated.div 
                  className="h-full transition-colors duration-500"
                  style={{ 
                    ...debtSpring,
                    backgroundColor: getHealthColor()
                  }}
                />
                {/* Health bar segments */}
                <div className="absolute inset-0 flex">
                  {[...Array(10)].map((_, i) => (
                    <div key={i} className="flex-1 border-r border-gray-700 last:border-r-0" />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Power Ups (Cash Buffer) */}
        <div className="bg-black border-2 border-blue-500 p-6 relative group">
          <div className="absolute inset-0 bg-blue-500 opacity-0 group-hover:opacity-10 transition-opacity" />
          
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-2">
              <span className="text-blue-500 font-mono text-xs">POWER UPS</span>
              <span className="text-2xl">⚡</span>
            </div>
            
            <div className="text-2xl font-mono font-bold text-blue-400">
              {formatCurrency(data.total_assets)}
            </div>
            
            {data.upcoming_payments > 0 && (
              <div className="mt-3 p-2 bg-yellow-900/30 border border-yellow-600">
                <div className="text-xs font-mono text-yellow-400 animate-pulse">
                  ⚠️ BOSS BATTLE IN 14 DAYS
                </div>
                <div className="text-sm font-mono text-yellow-300">
                  {formatCurrency(data.upcoming_payments)}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Retro Scanline Effect */}
      <style jsx>{`
        @keyframes scanline {
          0% { transform: translateY(-100%); }
          100% { transform: translateY(100%); }
        }
        
        .scanline::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(
            to bottom,
            transparent,
            rgba(0, 255, 65, 0.1),
            transparent
          );
          animation: scanline 8s linear infinite;
          pointer-events: none;
        }
      `}</style>
    </div>
  )
} 