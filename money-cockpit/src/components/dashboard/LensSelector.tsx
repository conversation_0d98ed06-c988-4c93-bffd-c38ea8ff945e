'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { Entity } from '@/types/database'

interface LensSelectorProps {
  currentLens: string
  onLensChange: (lens: string) => void
}

export default function LensSelector({ currentLens, onLensChange }: LensSelectorProps) {
  const [entities, setEntities] = useState<Entity[]>([])
  const supabase = createBrowserClient()

  useEffect(() => {
    fetchEntities()
  }, [])

  const fetchEntities = async () => {
    const { data, error } = await supabase
      .from('entities')
      .select('*')
      .eq('is_active', true)
      .order('type', { ascending: true })
      .order('name', { ascending: true })

    if (!error && data) {
      setEntities(data)
    }
  }

  const lenses = [
    { value: 'personal', label: 'Personal' },
    ...entities
      .filter(e => e.type === 'business')
      .map(e => ({ value: e.name, label: e.name })),
    { value: 'all', label: 'All' }
  ]

  return (
    <div className="flex gap-2 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
      {lenses.map((lens) => (
        <button
          key={lens.value}
          onClick={() => onLensChange(lens.value)}
          className={`
            px-4 py-2 rounded-md text-sm font-medium transition-all duration-200
            ${currentLens === lens.value
              ? 'bg-blue-600 text-white shadow-md'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }
          `}
        >
          {lens.label}
        </button>
      ))}
    </div>
  )
} 