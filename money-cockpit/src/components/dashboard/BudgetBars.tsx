'use client'

import { useEffect, useState } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'


interface BudgetBarProps {
  bucket?: string
  categoryName?: string
  budgetAmount: number
  spentAmount: number
  status: 'good' | 'warning' | 'over'
}

interface BudgetBarsProps {
  lens: string
}

export default function BudgetBars({ lens }: BudgetBarsProps) {
  const [budgets, setBudgets] = useState<BudgetBarProps[]>([])
  const [isLoading, setIsLoading] = useState(true)
  
  const supabase = createBrowserClient()

  useEffect(() => {
    fetchBudgetStatus()
  }, [lens])

  const fetchBudgetStatus = async () => {
    setIsLoading(true)
    
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return

    const { data, error } = await supabase
      .rpc('get_budget_status', { 
        p_user_id: user.id,
        p_lens: lens === 'personal' ? 'personal' : 'business'
      })

    if (!error && data) {
      setBudgets(data)
    }
    
    setIsLoading(false)
  }

  const getBarColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-500'
      case 'warning': return 'bg-yellow-500'
      case 'over': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getTextColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-400'
      case 'warning': return 'text-yellow-400'
      case 'over': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  if (isLoading) {
    return (
      <div className="bg-black border-2 border-purple-500 p-6">
        <div className="animate-pulse space-y-4">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="h-12 bg-gray-800 rounded" />
          ))}
        </div>
      </div>
    )
  }

  if (budgets.length === 0) {
    return (
      <div className="bg-black border-2 border-purple-500 p-6">
        <p className="text-center text-gray-500 font-mono text-sm">
          No budgets set up yet
        </p>
      </div>
    )
  }

  return (
    <div className="bg-black border-2 border-purple-500 p-6 relative">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-purple-500 font-mono text-sm font-bold">
          {lens === 'personal' ? 'BUCKET BUDGETS' : 'CATEGORY BUDGETS'}
        </h3>
        <span className="text-purple-400 font-mono text-xs animate-pulse">
          LIVE
        </span>
      </div>

      {/* Budget Bars */}
      <div className="space-y-4">
        {budgets.map((budget, index) => {
          const percentage = budget.budgetAmount > 0 
            ? Math.min((budget.spentAmount / budget.budgetAmount) * 100, 100)
            : 0

          return (
            <div key={index} className="space-y-2">
              {/* Label Row */}
              <div className="flex items-center justify-between">
                <span className="text-white font-mono text-xs">
                  {budget.bucket || budget.categoryName}
                </span>
                <span className={`font-mono text-xs ${getTextColor(budget.status)}`}>
                  ${budget.spentAmount.toFixed(0)} / ${budget.budgetAmount.toFixed(0)}
                </span>
              </div>

              {/* Progress Bar */}
              <div className="relative h-6 bg-gray-800 border border-gray-600">
                {/* Filled portion */}
                <div 
                  className={`absolute inset-y-0 left-0 ${getBarColor(budget.status)} transition-all duration-500`}
                  style={{ width: `${percentage}%` }}
                />
                
                {/* Segments */}
                <div className="absolute inset-0 flex">
                  {[...Array(10)].map((_, i) => (
                    <div key={i} className="flex-1 border-r border-gray-700 last:border-r-0" />
                  ))}
                </div>

                {/* Percentage text */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="font-mono text-xs text-white mix-blend-difference">
                    {percentage.toFixed(0)}%
                  </span>
                </div>

                {/* Warning indicators */}
                {budget.status === 'over' && (
                  <div className="absolute -right-8 top-1/2 -translate-y-1/2 text-red-500 animate-pulse">
                    <span className="font-mono text-xs">!</span>
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* Pixel corners */}
      <div className="absolute top-0 left-0 w-2 h-2 bg-purple-500" />
      <div className="absolute top-0 right-0 w-2 h-2 bg-purple-500" />
      <div className="absolute bottom-0 left-0 w-2 h-2 bg-purple-500" />
      <div className="absolute bottom-0 right-0 w-2 h-2 bg-purple-500" />
    </div>
  )
} 