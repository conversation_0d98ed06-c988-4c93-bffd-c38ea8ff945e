import { useWorkspaceUsers } from '@/lib/hooks/useWorkspaceUsers'

interface OwnerFilterProps {
  selectedOwner: string | null
  onOwnerChange: (owner: string | null) => void
  lens: string
}

export default function OwnerFilter({ selectedOwner, onOwnerChange, lens }: OwnerFilterProps) {
  const { users, loading } = useWorkspaceUsers()

  // Only show owner filter for personal lens
  if (lens !== 'personal') {
    return null
  }

  if (loading) {
    return (
      <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-8 w-32 rounded"></div>
    )
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Owner:
      </span>
      <div className="flex flex-wrap gap-1">
        {/* All filter */}
        <button
          onClick={() => onOwnerChange(null)}
          className={`
            px-3 py-1 text-xs font-mono rounded-full transition-all
            ${!selectedOwner
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
            }
          `}
        >
          ALL
        </button>

        {/* Shared filter */}
        <button
          onClick={() => onOwnerChange('shared')}
          className={`
            px-3 py-1 text-xs font-mono rounded-full transition-all
            ${selectedOwner === 'shared'
              ? 'bg-green-500 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
            }
          `}
        >
          SHARED
        </button>

        {/* User filters */}
        {users.map((user) => (
          <button
            key={user.user_id}
            onClick={() => onOwnerChange(user.user_id)}
            className={`
              px-3 py-1 text-xs font-mono rounded-full transition-all
              ${selectedOwner === user.user_id
                ? 'bg-purple-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }
            `}
          >
            {user.display_name || user.email.split('@')[0].toUpperCase()}
          </button>
        ))}
      </div>
    </div>
  )
} 