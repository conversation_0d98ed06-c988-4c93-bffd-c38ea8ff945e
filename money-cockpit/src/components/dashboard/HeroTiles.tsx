'use client'

import { DashboardData } from '@/types/database'
import { TrendingUp, TrendingDown, DollarSign, CreditCard, Target, FileText } from 'lucide-react'
import { useTheme } from '@/lib/contexts/ThemeContext'
import RetroHeroTiles from './RetroHeroTiles'

interface HeroTilesProps {
  data: DashboardData
}

export default function HeroTiles({ data }: HeroTilesProps) {
  const { theme } = useTheme()

  // Use retro tiles for retro theme
  if (theme === 'retro') {
    return <RetroHeroTiles data={data} />
  }

  // Normal theme tiles
  const tiles = [
    {
      label: 'Net Worth',
      value: data.net_worth,
      icon: DollarSign,
      color: data.net_worth >= 0 ? 'green' : 'red',
      trend: data.net_worth >= 0 ? 'up' : 'down'
    },
    {
      label: 'Total Debts',
      value: data.total_debt,
      icon: CreditCard,
      color: 'orange',
      trend: 'neutral'
    },
    {
      label: 'Cash Buffer',
      value: data.total_assets,
      icon: Target,
      color: 'blue',
      isCount: false
    },
    {
      label: 'Upcoming',
      value: data.upcoming_payments,
      icon: FileText,
      color: 'purple',
      isCount: false
    }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(Math.abs(amount))
  }

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800'
      case 'red':
        return 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800'
      case 'orange':
        return 'bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400 border-orange-200 dark:border-orange-800'
      case 'blue':
        return 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800'
      case 'purple':
        return 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800'
      default:
        return 'bg-gray-50 dark:bg-gray-900/20 text-gray-700 dark:text-gray-400 border-gray-200 dark:border-gray-800'
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      {tiles.map((tile) => {
        const Icon = tile.icon
        return (
          <div
            key={tile.label}
            className={`
              relative p-6 rounded-xl border-2 transition-all duration-200
              hover:shadow-lg hover:scale-105
              ${getColorClasses(tile.color)}
            `}
          >
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm font-medium opacity-80 mb-1">
                  {tile.label}
                </p>
                <p className="text-2xl font-bold">
                  {tile.isCount ? tile.value : formatCurrency(tile.value)}
                </p>
              </div>
              <div className="p-2 rounded-lg bg-white/50 dark:bg-black/20">
                <Icon className="h-5 w-5" />
              </div>
            </div>
            
            {tile.trend && (
              <div className="mt-3 flex items-center gap-1">
                {tile.trend === 'up' ? (
                  <TrendingUp className="h-4 w-4" />
                ) : tile.trend === 'down' ? (
                  <TrendingDown className="h-4 w-4" />
                ) : null}
                <span className="text-xs font-medium">
                  {tile.trend === 'up' ? 'Positive' : tile.trend === 'down' ? 'Negative' : 'Stable'}
                </span>
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
} 