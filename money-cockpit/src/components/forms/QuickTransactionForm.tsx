'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { useWorkspace } from '@/lib/contexts/WorkspaceContext'
import { Entity, Account } from '@/types/database'
import { X, Check } from 'lucide-react'
import { useCategories } from '@/lib/hooks/useCategories'

interface QuickTransactionFormProps {
  onClose: () => void
  onSuccess: () => void
}

export default function QuickTransactionForm({ onClose, onSuccess }: QuickTransactionFormProps) {
  const [entities, setEntities] = useState<Entity[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [debts, setDebts] = useState<Array<{id: string, name: string, balance: number}>>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isValid, setIsValid] = useState(false)
  
  const [formData, setFormData] = useState({
    entity_id: '',
    account_id: '',
    amount: '',
    date: new Date().toISOString().split('T')[0],
    category: '',
    bucket: '',
    memo: '',
    apply_to_debt: false,
    debt_id: ''
  })

  const supabase = createBrowserClient()
  const { currentWorkspaceId } = useWorkspace()
  
  // Use the categories hook
  const { categories } = useCategories()

  useEffect(() => {
    if (currentWorkspaceId) {
      fetchEntities()
    }
  }, [currentWorkspaceId])

  useEffect(() => {
    if (formData.entity_id) {
      fetchAccounts(formData.entity_id)
      fetchDebts(formData.entity_id)
    }
  }, [formData.entity_id])

  useEffect(() => {
    // Validate required fields
    const isComplete = 
      formData.entity_id &&
      formData.account_id &&
      formData.amount &&
      formData.date &&
      formData.category &&
      (!formData.apply_to_debt || formData.debt_id)
    
    setIsValid(!!isComplete && !isNaN(parseFloat(formData.amount)))
  }, [formData])

  const fetchEntities = async () => {
    if (!currentWorkspaceId) return
    
    const { data } = await supabase
      .from('entities')
      .select('*')
      .eq('workspace_id', currentWorkspaceId)
      .eq('is_active', true)
      .order('name')
    
    if (data) setEntities(data)
  }

  const fetchAccounts = async (entityId: string) => {
    const { data } = await supabase
      .from('accounts')
      .select('*')
      .eq('entity_id', entityId)
      .order('display_name')
    
    if (data) {
      setAccounts(data)
      // Reset account selection when entity changes
      setFormData(prev => ({ ...prev, account_id: '' }))
    }
  }

  const fetchDebts = async (entityId: string) => {
    const { data } = await supabase
      .from('debts')
      .select('id, name, balance')
      .eq('entity_id', entityId)
      .eq('is_active', true)
      .order('name')
    
    if (data) {
      setDebts(data)
      // Reset debt selection when entity changes
      setFormData(prev => ({ ...prev, debt_id: '', apply_to_debt: false }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!isValid || !currentWorkspaceId) return

    setIsLoading(true)

    const transactionData = {
      account_id: formData.account_id,
      amount: parseFloat(formData.amount),
      date: formData.date,
      category: formData.category,
      bucket: formData.bucket || null,
      memo: formData.memo || null,
      currency: 'AUD',
      debt_id: formData.apply_to_debt ? formData.debt_id : null,
      workspace_id: currentWorkspaceId
    }

    const { error } = await supabase
      .from('transactions')
      .insert(transactionData)

    // If this is a debt payment (negative amount), update debt's last_paid
    if (!error && formData.apply_to_debt && formData.debt_id && parseFloat(formData.amount) < 0) {
      await supabase
        .from('debts')
        .update({ last_paid: formData.date })
        .eq('id', formData.debt_id)
    }

    if (!error) {
      onSuccess()
      onClose()
    } else {
      alert('Error creating transaction: ' + error.message)
    }

    setIsLoading(false)
  }

  // Categories are now fetched from the database via the hook

  const buckets = ['Daily', 'Splurge', 'Smile', 'Fire']

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="fixed inset-0 bg-black/50" onClick={onClose} />
        
        <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Quick Transaction
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Entity Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Entity <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.entity_id}
                onChange={(e) => setFormData({ ...formData, entity_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              >
                <option value="">Select entity</option>
                {entities.map((entity) => (
                  <option key={entity.id} value={entity.id}>
                    {entity.name} ({entity.type})
                  </option>
                ))}
              </select>
            </div>

            {/* Account Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Account <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.account_id}
                onChange={(e) => setFormData({ ...formData, account_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
                disabled={!formData.entity_id}
              >
                <option value="">
                  {accounts.length === 0 ? 'No accounts yet - create one first' : 'Select account'}
                </option>
                {accounts.map((account) => (
                  <option key={account.id} value={account.id}>
                    {account.display_name} - {account.provider}
                  </option>
                ))}
              </select>
              {formData.entity_id && accounts.length === 0 && (
                <p className="text-xs text-amber-500 dark:text-amber-400 mt-1">
                  You need to create an account first. Go to Settings → Business Profiles → Edit Entity → Add Account
                </p>
              )}
            </div>

            {/* Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Amount <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="0.00"
                required
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Use negative for expenses, positive for income
              </p>
            </div>

            {/* Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              />
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Category <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              >
                <option value="">Select category</option>
                {categories.map((cat) => (
                  <option key={cat.id} value={cat.name}>{cat.name}</option>
                ))}
              </select>
            </div>

            {/* Bucket (Optional) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Bucket
              </label>
              <select
                value={formData.bucket}
                onChange={(e) => setFormData({ ...formData, bucket: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">No bucket</option>
                {buckets.map((bucket) => (
                  <option key={bucket} value={bucket}>{bucket}</option>
                ))}
              </select>
            </div>

            {/* Memo (Optional) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Memo
              </label>
              <textarea
                value={formData.memo}
                onChange={(e) => setFormData({ ...formData, memo: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                rows={2}
                placeholder="Optional notes..."
              />
            </div>

            {/* Apply to Debt */}
            {debts.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="apply_to_debt"
                    checked={formData.apply_to_debt}
                    onChange={(e) => setFormData({ 
                      ...formData, 
                      apply_to_debt: e.target.checked,
                      debt_id: e.target.checked ? formData.debt_id : ''
                    })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="apply_to_debt" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Apply to Debt?
                  </label>
                </div>
                
                {formData.apply_to_debt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Select Debt
                    </label>
                    <select
                      value={formData.debt_id}
                      onChange={(e) => setFormData({ ...formData, debt_id: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      required={formData.apply_to_debt}
                    >
                      <option value="">Select debt</option>
                      {debts.map((debt) => (
                        <option key={debt.id} value={debt.id}>
                          {debt.name} (${debt.balance?.toFixed(2) || '0.00'})
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            )}

            {/* Submit Button */}
            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!isValid || isLoading}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {isValid && <Check className="h-4 w-4 text-green-300" />}
                {isLoading ? 'Saving...' : 'Save Transaction'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
} 