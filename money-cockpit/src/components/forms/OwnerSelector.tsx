import { useWorkspaceUsers } from '@/lib/hooks/useWorkspaceUsers'

interface OwnerSelectorProps {
  value?: string
  onChange: (value: string) => void
  currentUserId?: string
  className?: string
  disabled?: boolean
}

export default function OwnerSelector({ 
  value, 
  onChange, 
  currentUserId, 
  className = '',
  disabled = false 
}: OwnerSelectorProps) {
  const { users, loading } = useWorkspaceUsers()

  if (loading) {
    return (
      <div className={`animate-pulse bg-gray-700 h-12 rounded ${className}`} />
    )
  }

  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-2">
        {/* Shared option */}
        <label
          className={`
            relative cursor-pointer border-2 px-4 py-2 text-center font-mono text-xs
            transition-all hover:scale-105
            ${value === 'shared' || !value
              ? 'border-green-500 bg-green-500/20 text-green-400'
              : 'border-gray-600 text-gray-400 hover:border-gray-500'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <input
            type="radio"
            name="owner"
            value="shared"
            checked={value === 'shared' || !value}
            onChange={(e) => onChange(e.target.value)}
            className="sr-only"
            disabled={disabled}
          />
          SHARED
        </label>

        {/* User options */}
        {users.map((user) => (
          <label
            key={user.user_id}
            className={`
              relative cursor-pointer border-2 px-4 py-2 text-center font-mono text-xs
              transition-all hover:scale-105
              ${value === user.user_id
                ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                : 'border-gray-600 text-gray-400 hover:border-gray-500'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            <input
              type="radio"
              name="owner"
              value={user.user_id}
              checked={value === user.user_id}
              onChange={(e) => onChange(e.target.value)}
              className="sr-only"
              disabled={disabled}
            />
            {user.display_name || user.email.split('@')[0].toUpperCase()}
            {user.user_id === currentUserId && ' (ME)'}
          </label>
        ))}
      </div>
    </div>
  )
} 