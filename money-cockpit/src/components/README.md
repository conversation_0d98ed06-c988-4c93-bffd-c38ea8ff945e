# Components Directory

This directory contains all reusable React components organized by functionality and reusability level. Components follow a hierarchical structure from base UI elements to complex feature components.

## 📁 Directory Structure

### `/ui` - Base Design System Components
Foundation components that form the design system:
- **Buttons**: Primary, secondary, icon buttons
- **Inputs**: Text fields, selects, checkboxes
- **Layout**: Containers, grids, spacing utilities
- **Typography**: Headings, text, labels
- **Feedback**: Alerts, toasts, loading states

### `/forms` - Form Components
Specialized form components with validation:
- **QuickTransactionForm**: Fast transaction entry
- **FormFields**: Reusable form field components
- **Validation**: Form validation utilities
- **Submission**: Form submission handlers

### `/modals` - Dialog Components
Modal dialogs for various actions:
- **AddAccountModal**: Create new accounts
- **AddBudgetModal**: Budget creation dialog
- **DebtModal**: Debt/loan management
- **GoalModal**: Financial goal setting
- **IncomeStreamModal**: Income stream setup
- **InvoiceModal**: Invoice creation
- **RecurringExpenseModal**: Recurring expense setup

### `/layouts` - Layout Components
Page structure and navigation:
- **AppLayout**: Main application layout with sidebar
- **SettingsLayout**: Settings page layout
- **AuthLayout**: Authentication page layout
- **Navigation**: Sidebar and mobile navigation

### `/dashboard` - Dashboard Components
Dashboard-specific components:
- **BudgetBars**: Budget progress visualization
- **InsightBanner**: Financial insights display
- **LensSelector**: Entity/view selector
- **MetricCards**: Key financial metrics
- **QuickActions**: Fast action buttons

### `/settings` - Settings Components
Settings page components:
- **TeamManagement**: Workspace team controls
- **CategoryManager**: Category management
- **PreferenceSettings**: User preferences
- **WorkspaceSettings**: Workspace configuration

## 🏗️ Component Architecture

### Component Hierarchy
```
ui/
├── Button.tsx          # Base button component
├── Input.tsx           # Base input component
└── Modal.tsx           # Base modal component

forms/
├── QuickTransactionForm.tsx  # Uses ui/Button, ui/Input
└── FormField.tsx            # Uses ui/Input, ui/Label

modals/
├── AddAccountModal.tsx      # Uses ui/Modal, forms/FormField
└── DebtModal.tsx           # Uses ui/Modal, forms/FormField

layouts/
├── AppLayout.tsx           # Uses ui/Button, dashboard/QuickActions
└── SettingsLayout.tsx      # Uses ui/Navigation
```

### Design Principles

#### 1. Single Responsibility
Each component has one clear purpose:
```typescript
// Good: Single purpose
function BudgetProgressBar({ budget, spent }) {
  return <div>/* Budget progress visualization */</div>
}

// Avoid: Multiple responsibilities
function BudgetDashboard() {
  // Don't combine progress, creation, and management
}
```

#### 2. Composition over Inheritance
Build complex components by composing simpler ones:
```typescript
function AddBudgetModal() {
  return (
    <Modal>
      <ModalHeader title="Add Budget" />
      <ModalBody>
        <BudgetForm />
      </ModalBody>
      <ModalFooter>
        <Button variant="primary">Save</Button>
      </ModalFooter>
    </Modal>
  )
}
```

#### 3. Props Interface Design
Clear, typed interfaces for all components:
```typescript
interface BudgetCardProps {
  budget: Budget
  onEdit?: (budget: Budget) => void
  onDelete?: (budgetId: string) => void
  showActions?: boolean
  variant?: 'default' | 'compact'
}
```

## 🎨 Styling Conventions

### Tailwind CSS Classes
- **Responsive**: Mobile-first responsive design
- **Dark Mode**: Support for light/dark themes
- **Consistent Spacing**: Use Tailwind spacing scale
- **Color System**: Consistent color palette

### Component Styling Pattern
```typescript
function Component({ variant = 'default', size = 'md' }) {
  const baseClasses = 'rounded-lg border transition-colors'
  const variantClasses = {
    default: 'bg-white border-gray-200',
    primary: 'bg-blue-50 border-blue-200',
    danger: 'bg-red-50 border-red-200'
  }
  const sizeClasses = {
    sm: 'p-2 text-sm',
    md: 'p-4 text-base',
    lg: 'p-6 text-lg'
  }
  
  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`}>
      {/* Component content */}
    </div>
  )
}
```

## 🔧 Development Patterns

### Component Template
```typescript
'use client' // Only if client-side features needed

import { useState } from 'react'
import { ComponentProps } from '@/types'

interface Props {
  // Define all props with TypeScript
  title: string
  onAction?: () => void
  variant?: 'default' | 'primary'
  children?: React.ReactNode
}

/**
 * Component description
 * @param title - The component title
 * @param onAction - Optional action handler
 * @param variant - Visual variant
 * @param children - Child components
 */
export default function ComponentName({ 
  title, 
  onAction, 
  variant = 'default',
  children 
}: Props) {
  const [state, setState] = useState(false)

  const handleAction = () => {
    setState(!state)
    onAction?.()
  }

  return (
    <div className="component-wrapper">
      <h2>{title}</h2>
      {children}
      <button onClick={handleAction}>
        Action
      </button>
    </div>
  )
}
```

### Custom Hooks Integration
```typescript
function DataComponent() {
  const { data, loading, error } = useCustomData()
  
  if (loading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />
  
  return <DataDisplay data={data} />
}
```

### Error Boundaries
```typescript
function ComponentWithErrorBoundary() {
  return (
    <ErrorBoundary fallback={<ErrorFallback />}>
      <ComplexComponent />
    </ErrorBoundary>
  )
}
```

## 🧪 Testing Strategy

### Component Testing
```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import Component from './Component'

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component title="Test" />)
    expect(screen.getByText('Test')).toBeInTheDocument()
  })

  it('handles user interaction', () => {
    const onAction = jest.fn()
    render(<Component title="Test" onAction={onAction} />)
    
    fireEvent.click(screen.getByRole('button'))
    expect(onAction).toHaveBeenCalled()
  })
})
```

### Accessibility Testing
- Screen reader compatibility
- Keyboard navigation
- ARIA labels and roles
- Color contrast compliance

## 📱 Responsive Design

### Breakpoint Strategy
- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px  
- **Desktop**: 1024px+

### Mobile-First Components
```typescript
function ResponsiveComponent() {
  return (
    <div className="
      flex flex-col space-y-4
      md:flex-row md:space-y-0 md:space-x-4
      lg:space-x-6
    ">
      {/* Component content */}
    </div>
  )
}
```

## 🎯 ADHD-Friendly Features

### Visual Feedback
- Clear loading states
- Success/error indicators
- Progress visualization
- Color-coded categories

### Interaction Design
- Large touch targets
- Clear button labels
- Consistent navigation
- Minimal cognitive load

### Achievement System
- Progress indicators
- Celebration animations
- Milestone notifications
- Gamification elements

## 🔄 State Management

### Local State
```typescript
function Component() {
  const [localState, setLocalState] = useState(initialValue)
  // Component-specific state
}
```

### Global State (Context)
```typescript
function Component() {
  const { workspace, setWorkspace } = useWorkspace()
  // Shared workspace state
}
```

### Server State (SWR)
```typescript
function Component() {
  const { data, error, mutate } = useSWR('/api/data', fetcher)
  // Server-synchronized state
}
```

## 📊 Performance Optimization

### React.memo for Pure Components
```typescript
const ExpensiveComponent = React.memo(function ExpensiveComponent({ data }) {
  return <div>{/* Expensive rendering */}</div>
})
```

### Lazy Loading
```typescript
const LazyModal = lazy(() => import('./HeavyModal'))

function Component() {
  return (
    <Suspense fallback={<Loading />}>
      <LazyModal />
    </Suspense>
  )
}
```

### Bundle Optimization
- Tree shaking for unused components
- Dynamic imports for heavy features
- Code splitting at component level

---

Each subdirectory contains specific documentation for its component implementations and usage patterns. 