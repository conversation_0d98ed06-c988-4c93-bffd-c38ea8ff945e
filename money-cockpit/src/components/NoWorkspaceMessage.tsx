'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createBrowserClient } from '@/lib/supabase-client'
import { AlertCircle } from 'lucide-react'

export default function NoWorkspaceMessage() {
  const router = useRouter()
  const supabase = createBrowserClient()

  useEffect(() => {
    // Check if user exists and refresh auth session
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/login')
      }
    }
    checkAuth()
  }, [router, supabase])

  const handleRefresh = () => {
    window.location.reload()
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div className="flex items-center justify-center w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full mx-auto mb-4">
          <AlertCircle className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
        </div>
        
        <h2 className="text-2xl font-bold text-center text-gray-900 dark:text-white mb-2">
          Setting Up Your Workspace
        </h2>
        
        <p className="text-center text-gray-600 dark:text-gray-400 mb-6">
          We&apos;re creating your personal workspace. This should only take a moment.
        </p>
        
        <button
          onClick={handleRefresh}
          className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
        >
          Refresh Page
        </button>
        
        <p className="text-sm text-center text-gray-500 dark:text-gray-400 mt-4">
          If this message persists, please try signing out and back in.
        </p>
      </div>
    </div>
  )
} 