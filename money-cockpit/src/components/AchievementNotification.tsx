'use client'

import { useEffect, useState } from 'react'
import { useSpring, animated } from '@react-spring/web'
import confetti from 'canvas-confetti'
import { achievements } from '@/lib/theme'

interface AchievementNotificationProps {
  achievementId: string
  onClose: () => void
}

export default function AchievementNotification({ achievementId, onClose }: AchievementNotificationProps) {
  const [isVisible, setIsVisible] = useState(false)
  const achievement = achievements[achievementId as keyof typeof achievements]

  const slideIn = useSpring({
    from: { transform: 'translateX(100%)', opacity: 0 },
    to: { 
      transform: isVisible ? 'translateX(0%)' : 'translateX(100%)', 
      opacity: isVisible ? 1 : 0 
    },
    config: { tension: 200, friction: 25 }
  })

  useEffect(() => {
    if (!achievement) return

    // Show notification
    setIsVisible(true)

    // Fire confetti
    const duration = 3000
    const animationEnd = Date.now() + duration
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 }

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min
    }

    const interval = setInterval(function() {
      const timeLeft = animationEnd - Date.now()

      if (timeLeft <= 0) {
        return clearInterval(interval)
      }

      const particleCount = 50 * (timeLeft / duration)
      
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
        colors: ['#00FF41', '#FF006E', '#0080FF', '#FFD700']
      })
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
        colors: ['#00FF41', '#FF006E', '#0080FF', '#FFD700']
      })
    }, 250)

    // Auto-hide after 5 seconds
    const hideTimeout = setTimeout(() => {
      setIsVisible(false)
      setTimeout(onClose, 500)
    }, 5000)

    return () => {
      clearInterval(interval)
      clearTimeout(hideTimeout)
    }
  }, [achievement, onClose])

  if (!achievement) return null

  return (
    <animated.div 
      style={slideIn}
      className="fixed top-4 right-4 z-50 max-w-sm"
    >
      <div className="bg-black border-4 border-yellow-400 p-6 relative overflow-hidden">
        {/* Animated background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 animate-pulse" />
        </div>

        {/* Content */}
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-3">
            <span className="text-4xl animate-bounce">{achievement.icon}</span>
            <div>
              <div className="text-yellow-400 font-mono text-xs mb-1">ACHIEVEMENT UNLOCKED!</div>
              <div className="text-white font-mono text-lg font-bold">{achievement.name}</div>
            </div>
          </div>
          
          <div className="text-gray-300 font-mono text-sm mb-3">
            {achievement.description}
          </div>
          
          <div className="flex items-center justify-between">
            <div className="text-yellow-400 font-mono text-sm">
              +{achievement.points} POINTS
            </div>
            <button
              onClick={() => {
                setIsVisible(false)
                setTimeout(onClose, 500)
              }}
              className="text-gray-400 hover:text-white font-mono text-xs"
            >
              [CLOSE]
            </button>
          </div>
        </div>

        {/* Pixel corners effect */}
        <div className="absolute top-0 left-0 w-2 h-2 bg-yellow-400" />
        <div className="absolute top-0 right-0 w-2 h-2 bg-yellow-400" />
        <div className="absolute bottom-0 left-0 w-2 h-2 bg-yellow-400" />
        <div className="absolute bottom-0 right-0 w-2 h-2 bg-yellow-400" />
      </div>
    </animated.div>
  )
} 