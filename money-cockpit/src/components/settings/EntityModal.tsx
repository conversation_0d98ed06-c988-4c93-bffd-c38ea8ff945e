'use client'

import { Fragment, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm } from 'react-hook-form'
import { createBrowserClient } from '@/lib/supabase-client'
import { Entity } from '@/types/database'
import { X, Check } from 'lucide-react'

interface EntityModalProps {
  entity: Entity | null
  onClose: () => void
  onSuccess: () => void
}

interface FormData {
  name: string
  type: 'personal' | 'business' | 'trust'
  abn_acn: string
  default_currency: string
  is_gst: boolean
}

const currencies = ['AUD', 'USD', 'EUR', 'GBP', 'NZD', 'SGD', 'HKD', 'JPY', 'CNY']

export default function EntityModal({ entity, onClose, onSuccess }: EntityModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const supabase = createBrowserClient()
  
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
  } = useForm<FormData>({
    defaultValues: {
      name: entity?.name || '',
      type: entity?.type || 'personal',
      abn_acn: entity?.abn_acn || '',
      default_currency: entity?.default_currency || 'AUD',
      is_gst: entity?.is_gst || false,
    },
    mode: 'onChange',
  })

  const watchType = watch('type')

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    setError(null)

    try {
      if (entity) {
        // Update existing entity
        const { error } = await supabase
          .from('entities')
          .update({
            name: data.name,
            type: data.type,
            abn_acn: data.type === 'business' ? data.abn_acn : null,
            default_currency: data.default_currency,
            is_gst: data.is_gst,
          })
          .eq('id', entity.id)
        
        if (error) throw error
      } else {
        // Create new entity
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) throw new Error('Not authenticated')
        
        const { error } = await supabase
          .from('entities')
          .insert({
            owner_id: user.id,
            name: data.name,
            type: data.type,
            abn_acn: data.type === 'business' ? data.abn_acn : null,
            default_currency: data.default_currency,
            is_gst: data.is_gst,
            is_active: true,
          })
        
        if (error) throw error
      }
      
      onSuccess()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Transition appear show={true} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-gray-900 dark:text-white flex items-center justify-between"
                >
                  {entity ? 'Edit Entity' : 'New Entity'}
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </Dialog.Title>

                <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-4">
                  {/* Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      {...register('name', {
                        required: 'Name is required',
                        maxLength: { value: 60, message: 'Name must be less than 60 characters' },
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      placeholder="My Business Pty Ltd"
                    />
                    {errors.name && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name.message}</p>
                    )}
                  </div>

                  {/* Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Type <span className="text-red-500">*</span>
                    </label>
                    <select
                      {...register('type', { required: 'Type is required' })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="personal">Personal</option>
                      <option value="business">Business</option>
                      <option value="trust">Trust</option>
                    </select>
                  </div>

                  {/* ABN/ACN - Only for business type */}
                  {watchType === 'business' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        ABN / ACN <span className="text-red-500">*</span>
                      </label>
                      <input
                        {...register('abn_acn', {
                          required: watchType === 'business' ? 'ABN/ACN is required for businesses' : false,
                          pattern: {
                            value: /^(\d{11}|\d{9})$/,
                            message: 'ABN must be 11 digits or ACN must be 9 digits',
                          },
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        placeholder="***********"
                      />
                      {errors.abn_acn && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.abn_acn.message}</p>
                      )}
                    </div>
                  )}

                  {/* Default Currency */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Default Currency
                    </label>
                    <select
                      {...register('default_currency')}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      {currencies.map((currency) => (
                        <option key={currency} value={currency}>
                          {currency}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* GST Registered */}
                  <div className="flex items-center">
                    <input
                      {...register('is_gst')}
                      type="checkbox"
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                      GST Registered
                    </label>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm">
                      {error}
                    </div>
                  )}

                  {/* Submit Buttons */}
                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={!isValid || isSubmitting}
                      className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                      {isValid && <Check className="h-4 w-4 text-green-300" />}
                      {isSubmitting ? 'Saving...' : entity ? 'Update' : 'Create'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 