import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    const { name, is_private = true } = await request.json()
    
    if (!name) {
      return NextResponse.json({ error: 'Workspace name is required' }, { status: 400 })
    }
    
    // Create a route handler client
    const supabase = await createRouteClient()

    // Try both session and user methods for better compatibility
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    console.log('Workspace API - Session:', !!session, 'User:', !!user)

    const currentUser = user || session?.user
    if (!currentUser) {
      return NextResponse.json({
        error: 'Not authenticated',
        details: {
          sessionError: sessionError?.message,
          userError: userError?.message
        }
      }, { status: 401 })
    }
    
    // Call the database function to create workspace
    const { data: workspace, error: workspaceError } = await supabase
      .rpc('create_workspace', {
        p_name: name,
        p_user_id: currentUser.id,
        p_is_private: is_private
      })
    
    if (workspaceError) {
      console.error('Error creating workspace:', workspaceError)
      return NextResponse.json({ error: workspaceError.message }, { status: 400 })
    }
    
    return NextResponse.json({ 
      message: 'Workspace created successfully',
      workspace
    })
    
  } catch (error) {
    console.error('Error creating workspace:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 