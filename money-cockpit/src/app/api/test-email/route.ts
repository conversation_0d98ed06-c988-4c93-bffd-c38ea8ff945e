import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { testEmailConfiguration } = await import('@/lib/email')
    
    const result = await testEmailConfiguration()
    
    return NextResponse.json({ 
      success: true,
      message: 'Email configuration test completed',
      details: result
    })
    
  } catch (error) {
    console.error('Email configuration test failed:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Email configuration test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
