import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    // Create a route handler client
    const supabase = await createRouteClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ 
        error: 'Not authenticated',
        details: userError?.message 
      }, { status: 401 })
    }

    // Get the token from request body
    const { token } = await request.json()
    
    if (!token) {
      return NextResponse.json({ 
        error: 'Invitation token is required' 
      }, { status: 400 })
    }

    // Use service role to bypass RLS issues
    const { createClient } = await import('@supabase/supabase-js')
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get the invitation
    const { data: invitation, error: inviteError } = await supabaseAdmin
      .from('workspace_invitations')
      .select('id, workspace_id, email, role, expires_at, accepted_at')
      .eq('token', token)
      .single()

    if (inviteError || !invitation) {
      return NextResponse.json({ 
        error: 'Invalid invitation token' 
      }, { status: 404 })
    }

    // Check if already accepted
    if (invitation.accepted_at) {
      return NextResponse.json({ 
        error: 'Invitation has already been accepted' 
      }, { status: 400 })
    }

    // Check if expired
    if (new Date(invitation.expires_at) < new Date()) {
      return NextResponse.json({ 
        error: 'Invitation has expired' 
      }, { status: 400 })
    }

    // Check if user's email matches invitation email
    if (user.email !== invitation.email) {
      return NextResponse.json({ 
        error: `This invitation is for ${invitation.email}, but you're signed in as ${user.email}` 
      }, { status: 403 })
    }

    // Check if user is already a member of this workspace
    const { data: existingMember } = await supabaseAdmin
      .from('workspace_users')
      .select('id')
      .eq('workspace_id', invitation.workspace_id)
      .eq('user_id', user.id)
      .single()

    if (existingMember) {
      // Mark invitation as accepted even though user is already a member
      await supabaseAdmin
        .from('workspace_invitations')
        .update({ accepted_at: new Date().toISOString() })
        .eq('id', invitation.id)

      return NextResponse.json({ 
        success: true,
        message: 'You are already a member of this workspace'
      })
    }

    // Create user profile if it doesn't exist
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .upsert({
        id: user.id,
        email: user.email,
        updated_at: new Date().toISOString()
      })

    if (profileError) {
      console.error('Error creating/updating profile:', profileError)
      // Continue anyway, profile creation is not critical
    }

    // Add user to workspace
    const { error: memberError } = await supabaseAdmin
      .from('workspace_users')
      .insert({
        workspace_id: invitation.workspace_id,
        user_id: user.id,
        role: invitation.role
      })

    if (memberError) {
      console.error('Error adding user to workspace:', memberError)
      return NextResponse.json({ 
        error: 'Failed to add user to workspace',
        details: memberError.message 
      }, { status: 500 })
    }

    // Mark invitation as accepted
    const { error: updateError } = await supabaseAdmin
      .from('workspace_invitations')
      .update({ accepted_at: new Date().toISOString() })
      .eq('id', invitation.id)

    if (updateError) {
      console.error('Error updating invitation:', updateError)
      // Don't fail the request if this fails, user is already added to workspace
    }

    return NextResponse.json({ 
      success: true,
      message: 'Successfully joined the workspace'
    })
    
  } catch (error) {
    console.error('Error in accept invitation API:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
