import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    // Create a route handler client
    const supabase = await createRouteClient()

    // Try both session and user methods
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    console.log('Test API - Session:', !!session, 'User:', !!user)
    console.log('Test API - Session Error:', sessionError?.message)
    console.log('Test API - User Error:', userError?.message)

    if ((!session && !user) || (userError && sessionError)) {
      return NextResponse.json({
        error: 'Not authenticated',
        details: {
          sessionError: sessionError?.message,
          userError: userError?.message,
          hasSession: !!session,
          hasUser: !!user
        }
      }, { status: 401 })
    }

    const currentUser = user || session?.user
    if (!currentUser) {
      return NextResponse.json({
        error: 'No user found',
        details: 'Neither user nor session.user is available'
      }, { status: 401 })
    }

    // Test if we can call the create_workspace function
    const { data: testResult, error: testError } = await supabase
      .rpc('create_workspace', {
        p_name: 'Test Workspace ' + Date.now(),
        p_user_id: currentUser.id
      })

    if (testError) {
      return NextResponse.json({
        error: 'Database function error',
        details: testError.message,
        code: testError.code,
        hint: testError.hint
      }, { status: 400 })
    }

    // Clean up the test workspace
    if (testResult && testResult.id) {
      await supabase
        .from('workspaces')
        .delete()
        .eq('id', testResult.id)
    }

    return NextResponse.json({
      success: true,
      user: {
        id: currentUser.id,
        email: currentUser.email
      },
      session: {
        hasSession: !!session,
        hasUser: !!user
      },
      testResult
    })
    
  } catch (error) {
    console.error('Error in test workspace creation:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
