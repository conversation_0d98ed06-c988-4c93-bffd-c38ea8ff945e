import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'

export async function DELETE(request: NextRequest) {
  try {
    // Create a route handler client
    const supabase = await createRouteClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ 
        error: 'Not authenticated',
        details: userError?.message 
      }, { status: 401 })
    }

    // Get parameters from query params
    const { searchParams } = new URL(request.url)
    const workspaceId = searchParams.get('workspace_id')
    const userId = searchParams.get('user_id')
    
    if (!workspaceId || !userId) {
      return NextResponse.json({ 
        error: 'Workspace ID and User ID are required' 
      }, { status: 400 })
    }

    // Use service role to bypass RLS issues
    const { createClient } = await import('@supabase/supabase-js')
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Remove the member
    const { error: deleteError } = await supabaseAdmin
      .from('workspace_users')
      .delete()
      .eq('workspace_id', workspaceId)
      .eq('user_id', userId)

    if (deleteError) {
      console.error('Error removing member:', deleteError)
      return NextResponse.json({ 
        error: 'Failed to remove member',
        details: deleteError.message 
      }, { status: 500 })
    }

    return NextResponse.json({ success: true })
    
  } catch (error) {
    console.error('Error in remove member API:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Create a route handler client
    const supabase = await createRouteClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ 
        error: 'Not authenticated',
        details: userError?.message 
      }, { status: 401 })
    }

    // Get parameters from request body
    const body = await request.json()
    const { workspace_id, user_id, role } = body
    
    if (!workspace_id || !user_id || !role) {
      return NextResponse.json({ 
        error: 'Workspace ID, User ID, and Role are required' 
      }, { status: 400 })
    }

    if (!['admin', 'viewer'].includes(role)) {
      return NextResponse.json({ 
        error: 'Role must be either admin or viewer' 
      }, { status: 400 })
    }

    // Use service role to bypass RLS issues
    const { createClient } = await import('@supabase/supabase-js')
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Update the member's role
    const { error: updateError } = await supabaseAdmin
      .from('workspace_users')
      .update({ role })
      .eq('workspace_id', workspace_id)
      .eq('user_id', user_id)

    if (updateError) {
      console.error('Error updating member role:', updateError)
      return NextResponse.json({ 
        error: 'Failed to update member role',
        details: updateError.message 
      }, { status: 500 })
    }

    return NextResponse.json({ success: true })
    
  } catch (error) {
    console.error('Error in update member role API:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
