import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    // Create a route handler client
    const supabase = await createRouteClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ 
        error: 'Not authenticated',
        details: userError?.message 
      }, { status: 401 })
    }

    // Get workspace_id from query params
    const { searchParams } = new URL(request.url)
    const workspaceId = searchParams.get('workspace_id')
    
    if (!workspaceId) {
      return NextResponse.json({ 
        error: 'Workspace ID is required' 
      }, { status: 400 })
    }

    // Use service role to bypass RLS issues
    const { createClient } = await import('@supabase/supabase-js')
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get workspace members
    const { data: membersData, error: membersError } = await supabaseAdmin
      .from('workspace_users')
      .select('user_id, role, created_at')
      .eq('workspace_id', workspaceId)

    if (membersError) {
      console.error('Error fetching members:', membersError)
      return NextResponse.json({ 
        error: 'Failed to fetch members',
        details: membersError.message 
      }, { status: 500 })
    }

    if (!membersData || membersData.length === 0) {
      return NextResponse.json({ members: [] })
    }

    // Get profiles for those users
    const userIds = membersData.map(m => m.user_id)
    const { data: profilesData, error: profilesError } = await supabaseAdmin
      .from('profiles')
      .select('id, email')
      .in('id', userIds)

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError)
      // Continue without emails
      return NextResponse.json({ 
        members: membersData.map(member => ({
          ...member,
          profiles: undefined
        }))
      })
    }

    // Combine the data
    const membersWithProfiles = membersData.map(member => ({
      ...member,
      profiles: profilesData?.find(p => p.id === member.user_id) ? {
        email: profilesData.find(p => p.id === member.user_id)!.email
      } : undefined
    }))

    return NextResponse.json({ members: membersWithProfiles })
    
  } catch (error) {
    console.error('Error in members API:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
