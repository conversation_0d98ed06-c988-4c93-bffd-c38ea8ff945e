# App Directory - Next.js App Router

This directory contains all pages, layouts, and API routes using Next.js 15 App Router conventions. Each folder represents a route in the application.

## 📁 Directory Structure

### Authentication Routes
- **`(auth)/`** - Route group for authentication pages
  - `login/` - User login and registration page

### Core Application Pages
- **`dashboard/`** - Main financial dashboard and overview
- **`income/`** - Income stream management and tracking
- **`expenses/`** - Expense tracking and categorization
- **`assets/`** - Asset portfolio management
- **`liabilities/`** - Debt and liability tracking
- **`budgets/`** - Budget creation and monitoring
- **`invoices/`** - Invoice management system
  - `upload/` - Invoice upload and processing
- **`recurring-expenses/`** - Recurring expense management
- **`goals/`** - Financial goal setting and tracking

### Settings & Configuration
- **`settings/`** - Application settings and preferences
  - `team/` - Workspace team management
  - `categories/` - Transaction category management
  - `budgets/` - Budget configuration
  - `buckets/` - Financial bucket management
  - `prefs/` - User preferences

### API Routes
- **`api/`** - Server-side API endpoints
  - `invite/` - User invitation system

## 🏗️ App Router Features

### File Conventions
- **`page.tsx`** - Page component (publicly accessible route)
- **`layout.tsx`** - Shared layout for route segment
- **`loading.tsx`** - Loading UI for route segment
- **`error.tsx`** - Error UI for route segment
- **`not-found.tsx`** - 404 UI for route segment

### Route Groups
- **`(auth)`** - Groups authentication routes without affecting URL structure
- Allows shared layouts without adding route segments

### Nested Layouts
```
app/
├── layout.tsx          # Root layout (applies to all routes)
├── dashboard/
│   ├── layout.tsx      # Dashboard layout
│   └── page.tsx        # Dashboard page
└── settings/
    ├── layout.tsx      # Settings layout
    ├── page.tsx        # Settings overview
    └── team/
        └── page.tsx    # Team settings page
```

## 🔧 Implementation Patterns

### Page Components
```typescript
// Standard page component structure
export default function PageName() {
  return (
    <AppLayout>
      <div className="container mx-auto p-6">
        {/* Page content */}
      </div>
    </AppLayout>
  )
}
```

### Layout Components
```typescript
// Layout component with shared navigation
export default function SectionLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="section-layout">
      <SectionNavigation />
      <main>{children}</main>
    </div>
  )
}
```

### API Routes
```typescript
// API route handler
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  // Handle GET request
  return NextResponse.json({ data: 'response' })
}

export async function POST(request: NextRequest) {
  // Handle POST request
  const body = await request.json()
  return NextResponse.json({ success: true })
}
```

## 🛡️ Security & Middleware

### Authentication
- **Middleware**: Protects routes requiring authentication
- **Route Guards**: Server-side authentication checks
- **Session Management**: Supabase Auth integration

### Workspace Access Control
- **RLS Policies**: Database-level access control
- **Workspace Context**: Client-side workspace management
- **Permission Checks**: Role-based access validation

## 📱 Responsive Design

### Mobile-First Approach
- All pages designed for mobile devices first
- Progressive enhancement for larger screens
- Touch-friendly interface elements

### Layout Adaptations
- **Mobile**: Single column, hamburger navigation
- **Tablet**: Sidebar navigation, optimized spacing
- **Desktop**: Full sidebar, multi-column layouts

## 🎯 ADHD-Friendly Features

### Visual Hierarchy
- Clear section separation
- Consistent color coding
- Visual progress indicators

### Navigation
- Persistent sidebar navigation
- Breadcrumb trails
- Quick action buttons

### Cognitive Load Reduction
- Single-purpose pages
- Progressive disclosure
- Clear call-to-action buttons

## 🔄 Data Flow

### Server Components
- Fetch data at build time or request time
- Reduce client-side JavaScript
- Improve initial page load performance

### Client Components
- Interactive elements requiring state
- Real-time updates with Supabase
- Form handling and validation

### Hybrid Approach
```typescript
// Server component for initial data
async function ServerComponent() {
  const data = await fetchServerData()
  return <ClientComponent initialData={data} />
}

// Client component for interactivity
'use client'
function ClientComponent({ initialData }) {
  const [data, setData] = useState(initialData)
  // Interactive logic
}
```

## 📊 Performance Optimization

### Code Splitting
- Automatic route-based code splitting
- Dynamic imports for heavy components
- Lazy loading for non-critical features

### Caching Strategy
- Static generation where possible
- ISR for dynamic content
- Client-side caching with SWR

### Bundle Optimization
- Tree shaking for unused code
- Image optimization with Next.js Image
- Font optimization with next/font

---

Each subdirectory contains its own README with specific implementation details and component documentation. 