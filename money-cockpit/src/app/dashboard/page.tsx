'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { useWorkspace } from '@/lib/contexts/WorkspaceContext'
import { DashboardData } from '@/types/database'
import LensSelector from '@/components/dashboard/LensSelector'
import OwnerFilter from '@/components/dashboard/OwnerFilter'
import InsightBanner from '@/components/dashboard/InsightBanner'
import HeroTiles from '@/components/dashboard/HeroTiles'
import BudgetBars from '@/components/dashboard/BudgetBars'
import GoalsWidget from '@/components/dashboard/GoalsWidget'
import QuickAddModal from '@/components/QuickAddModal'
import ThemeToggle from '@/components/ui/ThemeToggle'
import AppLayout from '@/components/layouts/AppLayout'
import NoWorkspaceMessage from '@/components/NoWorkspaceMessage'
import { Plus } from 'lucide-react'

export default function DashboardPage() {
  const [lens, setLens] = useState('personal')
  const [selectedOwner, setSelectedOwner] = useState<string | null>(null)
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isQuickAddOpen, setIsQuickAddOpen] = useState(false)
  
  const { currentWorkspaceId, isLoading: workspaceLoading, workspaces } = useWorkspace()
  const supabase = createBrowserClient()

  useEffect(() => {
    if (!workspaceLoading && currentWorkspaceId) {
      fetchUserPrefs()
    }
  }, [currentWorkspaceId, workspaceLoading])

  useEffect(() => {
    if (!workspaceLoading && currentWorkspaceId) {
      fetchDashboardData()
    }
  }, [lens, selectedOwner, currentWorkspaceId, workspaceLoading])

  const fetchUserPrefs = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return

    const { data: prefs } = await supabase.rpc('get_or_create_user_prefs', {
      p_user_id: user.id
    })

    if (prefs && prefs.default_lens) {
      setLens(prefs.default_lens)
    }
  }

  const fetchDashboardData = async () => {
    if (!currentWorkspaceId) return
    
    setIsLoading(true)
    const { data, error } = await supabase
      .rpc('get_dashboard', { 
        p_lens: lens,
        p_workspace_id: currentWorkspaceId
      })

    if (!error && data) {
      setDashboardData(data)
    }
    setIsLoading(false)
  }

  if (workspaceLoading) {
    return (
      <AppLayout title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    )
  }

  // Show no workspace message if user has no workspaces
  if (!workspaceLoading && (!workspaces || workspaces.length === 0)) {
    return <NoWorkspaceMessage />
  }

  return (
    <AppLayout title="Dashboard">
      <div className="py-6 px-4 sm:px-6 lg:px-8">
        {/* Header with Lens Selector */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white hidden lg:block">
              Dashboard
            </h1>
            <div className="hidden lg:flex items-center gap-4">
              <ThemeToggle />
              <button 
                onClick={() => setIsQuickAddOpen(true)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Plus className="h-5 w-5" />
                Quick Add
              </button>
            </div>
          </div>
          <LensSelector currentLens={lens} onLensChange={setLens} />
          
          {/* Owner Filter - Only show for personal lens */}
          <div className="mt-4">
            <OwnerFilter 
              selectedOwner={selectedOwner}
              onOwnerChange={setSelectedOwner}
              lens={lens}
            />
          </div>
        </div>

        {/* Dashboard Content */}
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : dashboardData ? (
          <>
            <InsightBanner lens={lens} />
            <HeroTiles data={dashboardData} />
            
            {/* Dashboard Widgets Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
              {/* Budget Bars */}
              <BudgetBars lens={lens} />
              
              {/* Goals Widget - Show when lens is 'all' */}
              {lens === 'all' && <GoalsWidget lens={lens} />}
            </div>
            
            {/* Additional dashboard components would go here */}
            {/* Based on lens type, show:
                - BarefootBuckets (personal)
                - SalesRings (business)
                - DebtSnowballMiniChart
                - UpcomingPaymentsList
            */}
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">
              No data available. Start by adding your first transaction.
            </p>
          </div>
        )}
      </div>

      {/* Floating Action Button (Mobile) */}
      <button 
        onClick={() => setIsQuickAddOpen(true)}
        className="lg:hidden fixed bottom-6 right-6 w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center z-50"
      >
        <Plus className="h-6 w-6" />
      </button>

      {/* Quick Add Modal */}
      <QuickAddModal
        isOpen={isQuickAddOpen}
        onClose={() => setIsQuickAddOpen(false)}
        onSuccess={() => {
          fetchDashboardData()
          setIsQuickAddOpen(false)
        }}
      />
    </AppLayout>
  )
} 