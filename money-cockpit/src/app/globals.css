@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Retro Gaming Animations */
@keyframes pixelBounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-1px, 1px); }
  40% { transform: translate(-1px, -1px); }
  60% { transform: translate(1px, 1px); }
  80% { transform: translate(1px, -1px); }
  100% { transform: translate(0); }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes scanline {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

/* Pixel Art Corners */
.pixel-corners {
  clip-path: polygon(
    0 8px, 8px 8px, 8px 0,
    calc(100% - 8px) 0, calc(100% - 8px) 8px, 100% 8px,
    100% calc(100% - 8px), calc(100% - 8px) calc(100% - 8px), calc(100% - 8px) 100%,
    8px 100%, 8px calc(100% - 8px), 0 calc(100% - 8px)
  );
}

/* Retro Text Shadow */
.retro-text {
  text-shadow: 2px 2px 0px rgba(0, 255, 65, 0.5);
}

/* Arcade Glow */
.arcade-glow {
  box-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
}

/* Font Variables */
:root {
  --font-pixel: 'Press Start 2P', monospace;
}

/* Theme-specific styles */
.retro-theme {
  /* Keep all the retro styling active */
}

.normal-theme {
  /* Override retro styles for normal theme */
}

/* Retro theme specific overrides */
.retro-theme .retro-only {
  display: block;
}

.normal-theme .retro-only {
  display: none;
}

/* Normal theme specific overrides */
.normal-theme .normal-only {
  display: block;
}

.retro-theme .normal-only {
  display: none;
}
