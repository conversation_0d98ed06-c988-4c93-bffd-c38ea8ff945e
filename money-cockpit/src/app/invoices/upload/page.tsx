'use client'

import { useState, useCallback, useEffect } from 'react'
import { useDropzone } from 'react-dropzone'
import { createBrowserClient } from '@/lib/supabase-client'
import { Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react'

interface Entity {
  id: string
  name: string
  type: string
}

export default function InvoiceUploadPage() {
  const [entities, setEntities] = useState<Entity[]>([])
  const [selectedEntity, setSelectedEntity] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [uploadMessage, setUploadMessage] = useState('')

  const supabase = createBrowserClient()

  // Fetch entities on component mount
  useEffect(() => {
    fetchEntities()
  }, [])

  const fetchEntities = async () => {
    const { data } = await supabase
      .from('entities')
      .select('id, name, type')
      .eq('is_active', true)
      .order('name')
    
    if (data) setEntities(data)
  }

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!selectedEntity) {
      setUploadStatus('error')
      setUploadMessage('Please select an entity first')
      return
    }

    if (acceptedFiles.length === 0) return

    const file = acceptedFiles[0]
    setIsUploading(true)
    setUploadStatus('idle')

    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      // Generate unique file path
      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
      const filePath = `${user.id}/${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, '0')}/${fileName}`

      // Upload file to storage
      const { error: uploadError } = await supabase.storage
        .from('docs')
        .upload(filePath, file)

      if (uploadError) throw uploadError

      // Call OCR parsing function
      const { error: parseError } = await supabase.functions.invoke('ocr_parse_invoice', {
        body: {
          storage_path: filePath,
          entity_id: selectedEntity,
          user_id: user.id
        }
      })

      if (parseError) throw parseError

      setUploadStatus('success')
      setUploadMessage('Invoice uploaded and parsed successfully!')
      
      // Reset form
      setSelectedEntity('')
      
    } catch (error) {
      console.error('Upload error:', error)
      setUploadStatus('error')
      setUploadMessage(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setIsUploading(false)
    }
  }, [selectedEntity, supabase])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg']
    },
    maxFiles: 1,
    disabled: isUploading
  })

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Upload Invoice</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Upload PDF or image files to automatically extract invoice details
        </p>
      </div>

      <div className="max-w-2xl space-y-6">
        {/* Entity Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Entity <span className="text-red-500">*</span>
          </label>
          <select
            value={selectedEntity}
            onChange={(e) => setSelectedEntity(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            required
          >
            <option value="">Select entity</option>
            {entities.map((entity) => (
              <option key={entity.id} value={entity.id}>
                {entity.name} ({entity.type})
              </option>
            ))}
          </select>
        </div>

        {/* File Upload Area */}
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
            ${isDragActive 
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }
            ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <input {...getInputProps()} />
          
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              {isUploading ? (
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              ) : (
                <Upload className="h-8 w-8 text-gray-400" />
              )}
            </div>
            
            <div>
              <p className="text-lg font-medium text-gray-900 dark:text-white">
                {isDragActive ? 'Drop the file here' : 'Drag & drop your invoice'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                or click to select a file
              </p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
                Supports PDF, PNG, JPG (max 10MB)
              </p>
            </div>
          </div>
        </div>

        {/* Upload Status */}
        {uploadStatus !== 'idle' && (
          <div className={`
            p-4 rounded-lg flex items-start gap-3
            ${uploadStatus === 'success' 
              ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400' 
              : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400'
            }
          `}>
            {uploadStatus === 'success' ? (
              <CheckCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
            ) : (
              <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
            )}
            <div>
              <p className="font-medium">
                {uploadStatus === 'success' ? 'Success!' : 'Error'}
              </p>
              <p className="text-sm mt-1">{uploadMessage}</p>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <div className="flex items-start gap-3">
            <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100">How it works</h3>
              <ul className="text-sm text-blue-700 dark:text-blue-300 mt-2 space-y-1">
                <li>• Upload your invoice (PDF or image)</li>
                <li>• Our system extracts amount, due date, and vendor</li>
                <li>• Invoices are automatically matched to payments</li>
                <li>• View all invoices in the main Invoices page</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 