'use client'

import { Plus } from 'lucide-react'
import { useState } from 'react'
import RecurringExpenseModal from '@/components/modals/RecurringExpenseModal'
import AppLayout from '@/components/layouts/AppLayout'

export default function RecurringExpensesPage() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <AppLayout title="Recurring Expenses">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Recurring Expenses</h1>
          <button
            onClick={() => setIsModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            <Plus className="h-5 w-5" />
            Add Recurring Expense
          </button>
        </div>

        <div className="bg-black border-2 border-purple-500 p-8">
          <p className="text-center text-gray-500 font-mono">
            Recurring expenses list coming soon...
          </p>
        </div>

        <RecurringExpenseModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSuccess={() => setIsModalOpen(false)}
        />
      </div>
    </AppLayout>
  )
} 