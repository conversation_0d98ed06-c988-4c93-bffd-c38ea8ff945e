'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { Plus, Save, X, Edit2, Trash2 } from 'lucide-react'
import AddBudgetModal from '@/components/modals/AddBudgetModal'

interface Budget {
  id: string
  entity_id: string | null
  entity_name?: string
  scope: 'personal' | 'business'
  bucket: string | null
  category_id: string | null
  category_name?: string
  period: 'weekly' | 'monthly'
  amount: number
  currency: string
}

export default function BudgetsPage() {
  const [budgets, setBudgets] = useState<Budget[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editAmount, setEditAmount] = useState<number>(0)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  
  const supabase = createBrowserClient()

  useEffect(() => {
    fetchBudgets()
  }, [])

  const fetchBudgets = async () => {
    setIsLoading(true)
    
    const { data, error } = await supabase
      .from('budgets')
      .select(`
        *,
        entities!budgets_entity_id_fkey(name),
        categories!budgets_category_id_fkey(name)
      `)
      .order('scope', { ascending: true })
      .order('entity_id', { ascending: true })
      .order('bucket', { ascending: true })

    if (!error && data) {
      setBudgets(data.map(b => ({
        ...b,
        entity_name: b.entities?.name,
        category_name: b.categories?.name
      })))
    }
    
    setIsLoading(false)
  }

  const handleEdit = (budget: Budget) => {
    setEditingId(budget.id)
    setEditAmount(budget.amount)
  }

  const handleSave = async (budgetId: string) => {
    const { error } = await supabase
      .from('budgets')
      .update({ amount: editAmount })
      .eq('id', budgetId)

    if (!error) {
      setBudgets(budgets.map(b => 
        b.id === budgetId ? { ...b, amount: editAmount } : b
      ))
      setEditingId(null)
    }
  }

  const handleDelete = async (budgetId: string) => {
    if (!confirm('Delete this budget?')) return

    const { error } = await supabase
      .from('budgets')
      .delete()
      .eq('id', budgetId)

    if (!error) {
      setBudgets(budgets.filter(b => b.id !== budgetId))
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const personalBudgets = budgets.filter(b => b.scope === 'personal')
  const businessBudgets = budgets.filter(b => b.scope === 'business')

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Budgets</h1>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus className="h-5 w-5" />
          Add Budget
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Personal Budgets */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 font-mono">
              PERSONAL BUDGETS
            </h2>
            <div className="bg-black border-2 border-green-500 overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-green-500">
                    <th className="px-4 py-3 text-left text-xs font-mono text-green-500">BUCKET</th>
                    <th className="px-4 py-3 text-left text-xs font-mono text-green-500">PERIOD</th>
                    <th className="px-4 py-3 text-right text-xs font-mono text-green-500">AMOUNT</th>
                    <th className="px-4 py-3 text-right text-xs font-mono text-green-500">ACTIONS</th>
                  </tr>
                </thead>
                <tbody>
                  {personalBudgets.length === 0 ? (
                    <tr>
                      <td colSpan={4} className="px-4 py-8 text-center text-gray-500 font-mono text-sm">
                        No personal budgets yet
                      </td>
                    </tr>
                  ) : (
                    personalBudgets.map((budget) => (
                      <tr key={budget.id} className="border-b border-gray-700 hover:bg-green-500/10">
                        <td className="px-4 py-3 text-sm font-mono text-white">
                          {budget.bucket}
                        </td>
                        <td className="px-4 py-3 text-sm font-mono text-gray-400">
                          {budget.period.toUpperCase()}
                        </td>
                        <td className="px-4 py-3 text-right">
                          {editingId === budget.id ? (
                            <input
                              type="number"
                              value={editAmount}
                              onChange={(e) => setEditAmount(Number(e.target.value))}
                              className="w-24 bg-gray-900 border border-green-500 text-white font-mono px-2 py-1 text-sm text-right"
                              autoFocus
                            />
                          ) : (
                            <span className="text-sm font-mono text-green-400">
                              {formatCurrency(budget.amount)}
                            </span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-right">
                          {editingId === budget.id ? (
                            <div className="flex items-center justify-end gap-2">
                              <button
                                onClick={() => handleSave(budget.id)}
                                className="text-green-500 hover:text-green-400"
                              >
                                <Save className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => setEditingId(null)}
                                className="text-gray-500 hover:text-gray-400"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                          ) : (
                            <div className="flex items-center justify-end gap-2">
                              <button
                                onClick={() => handleEdit(budget)}
                                className="text-gray-500 hover:text-white"
                              >
                                <Edit2 className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(budget.id)}
                                className="text-red-500 hover:text-red-400"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          )}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Business Budgets */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 font-mono">
              BUSINESS BUDGETS
            </h2>
            <div className="bg-black border-2 border-blue-500 overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-blue-500">
                    <th className="px-4 py-3 text-left text-xs font-mono text-blue-500">ENTITY</th>
                    <th className="px-4 py-3 text-left text-xs font-mono text-blue-500">CATEGORY</th>
                    <th className="px-4 py-3 text-left text-xs font-mono text-blue-500">PERIOD</th>
                    <th className="px-4 py-3 text-right text-xs font-mono text-blue-500">AMOUNT</th>
                    <th className="px-4 py-3 text-right text-xs font-mono text-blue-500">ACTIONS</th>
                  </tr>
                </thead>
                <tbody>
                  {businessBudgets.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="px-4 py-8 text-center text-gray-500 font-mono text-sm">
                        No business budgets yet
                      </td>
                    </tr>
                  ) : (
                    businessBudgets.map((budget) => (
                      <tr key={budget.id} className="border-b border-gray-700 hover:bg-blue-500/10">
                        <td className="px-4 py-3 text-sm font-mono text-white">
                          {budget.entity_name}
                        </td>
                        <td className="px-4 py-3 text-sm font-mono text-gray-400">
                          {budget.category_name}
                        </td>
                        <td className="px-4 py-3 text-sm font-mono text-gray-400">
                          {budget.period.toUpperCase()}
                        </td>
                        <td className="px-4 py-3 text-right">
                          {editingId === budget.id ? (
                            <input
                              type="number"
                              value={editAmount}
                              onChange={(e) => setEditAmount(Number(e.target.value))}
                              className="w-24 bg-gray-900 border border-blue-500 text-white font-mono px-2 py-1 text-sm text-right"
                              autoFocus
                            />
                          ) : (
                            <span className="text-sm font-mono text-blue-400">
                              {formatCurrency(budget.amount)}
                            </span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-right">
                          {editingId === budget.id ? (
                            <div className="flex items-center justify-end gap-2">
                              <button
                                onClick={() => handleSave(budget.id)}
                                className="text-blue-500 hover:text-blue-400"
                              >
                                <Save className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => setEditingId(null)}
                                className="text-gray-500 hover:text-gray-400"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                          ) : (
                            <div className="flex items-center justify-end gap-2">
                              <button
                                onClick={() => handleEdit(budget)}
                                className="text-gray-500 hover:text-white"
                              >
                                <Edit2 className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(budget.id)}
                                className="text-red-500 hover:text-red-400"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          )}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Add Budget Modal */}
      <AddBudgetModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={() => {
          fetchBudgets()
          setIsAddModalOpen(false)
        }}
      />
    </div>
  )
} 