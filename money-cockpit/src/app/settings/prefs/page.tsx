'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { Settings } from 'lucide-react'

interface UserPrefs {
  pay_day: string
  default_lens: string
  date_fmt: string
}

const WEEKDAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

const DATE_FORMATS = [
  { value: 'd MMM', label: '1 Jan' },
  { value: 'dd/MM', label: '01/01' },
  { value: 'MM/dd', label: '01/01' },
  { value: 'MMM d', label: 'Jan 1' },
  { value: 'd MMMM', label: '1 January' }
]

export default function PrefsPage() {
  const [prefs, setPrefs] = useState<UserPrefs>({
    pay_day: 'Friday',
    default_lens: 'personal',
    date_fmt: 'd MMM'
  })
  const [entities, setEntities] = useState<Array<{ id: string; name: string }>>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  
  const supabase = createBrowserClient()

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    setIsLoading(true)
    
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      // Fetch or create user preferences
      const { data: prefsData } = await supabase.rpc('get_or_create_user_prefs', {
        p_user_id: user.id
      })

      if (prefsData) {
        setPrefs({
          pay_day: prefsData.pay_day,
          default_lens: prefsData.default_lens,
          date_fmt: prefsData.date_fmt
        })
      }

      // Fetch entities for lens options
      const { data: entitiesData } = await supabase
        .from('entities')
        .select('id, name')
        .eq('is_active', true)
        .eq('type', 'business')
        .order('name')

      if (entitiesData) {
        setEntities(entitiesData)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      const { error } = await supabase
        .from('user_prefs')
        .upsert({
          user_id: user.id,
          ...prefs,
          updated_at: new Date().toISOString()
        })

      if (error) throw error

      alert('Preferences saved successfully!')
    } catch (error) {
      console.error('Error saving preferences:', error)
      alert('Failed to save preferences')
    } finally {
      setIsSaving(false)
    }
  }

  const getLensOptions = () => {
    const options = [
      { value: 'personal', label: 'Personal' },
      { value: 'all', label: 'All Entities' }
    ]
    
    // Add business entities as options
    entities.forEach(entity => {
      options.push({ value: entity.id, label: entity.name })
    })
    
    return options
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
          <Settings className="h-6 w-6" />
          User Preferences
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Configure your personal settings and defaults.
        </p>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="max-w-2xl">
          <div className="bg-black border-2 border-cyan-500 p-6 space-y-6">
            {/* Pay Day */}
            <div>
              <label className="text-cyan-500 font-mono text-xs mb-2 block">
                PAY DAY
              </label>
              <select
                value={prefs.pay_day}
                onChange={(e) => setPrefs({ ...prefs, pay_day: e.target.value })}
                className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
              >
                {WEEKDAYS.map(day => (
                  <option key={day} value={day}>{day}</option>
                ))}
              </select>
              <p className="text-gray-400 font-mono text-xs mt-1">
                When your regular income hits your account
              </p>
            </div>

            {/* Default Lens */}
            <div>
              <label className="text-cyan-500 font-mono text-xs mb-2 block">
                DEFAULT LENS
              </label>
              <select
                value={prefs.default_lens}
                onChange={(e) => setPrefs({ ...prefs, default_lens: e.target.value })}
                className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
              >
                {getLensOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <p className="text-gray-400 font-mono text-xs mt-1">
                Which view to show when you open the dashboard
              </p>
            </div>

            {/* Date Format */}
            <div>
              <label className="text-cyan-500 font-mono text-xs mb-2 block">
                DATE FORMAT
              </label>
              <select
                value={prefs.date_fmt}
                onChange={(e) => setPrefs({ ...prefs, date_fmt: e.target.value })}
                className="w-full bg-gray-900 border-2 border-gray-600 text-white font-mono px-4 py-3 focus:border-cyan-500 focus:outline-none"
              >
                {DATE_FORMATS.map(format => (
                  <option key={format.value} value={format.value}>
                    {format.label} ({format.value})
                  </option>
                ))}
              </select>
              <p className="text-gray-400 font-mono text-xs mt-1">
                How dates appear throughout the app
              </p>
            </div>

            {/* Save Button */}
            <div className="pt-4">
              <button
                onClick={handleSave}
                disabled={isSaving}
                className={`
                  w-full border-2 font-mono py-3 transition-all
                  ${isSaving
                    ? 'bg-gray-800 border-gray-600 text-gray-500 cursor-not-allowed'
                    : 'bg-cyan-500 border-cyan-400 text-black hover:scale-105'
                  }
                `}
              >
                {isSaving ? 'SAVING...' : 'SAVE PREFERENCES'}
              </button>
            </div>

            {/* Pixel corners */}
            <div className="absolute top-0 left-0 w-2 h-2 bg-cyan-500" />
            <div className="absolute top-0 right-0 w-2 h-2 bg-cyan-500" />
            <div className="absolute bottom-0 left-0 w-2 h-2 bg-cyan-500" />
            <div className="absolute bottom-0 right-0 w-2 h-2 bg-cyan-500" />
          </div>

          {/* Preview Box */}
          <div className="mt-6 bg-gray-900 border-2 border-gray-700 p-4">
            <p className="text-gray-400 font-mono text-xs mb-2">PREVIEW</p>
            <div className="space-y-1">
              <p className="text-white font-mono text-sm">
                Pay day: <span className="text-cyan-400">{prefs.pay_day}</span>
              </p>
              <p className="text-white font-mono text-sm">
                Default view: <span className="text-cyan-400">
                  {getLensOptions().find(o => o.value === prefs.default_lens)?.label || prefs.default_lens}
                </span>
              </p>
              <p className="text-white font-mono text-sm">
                Date example: <span className="text-cyan-400">
                  {DATE_FORMATS.find(f => f.value === prefs.date_fmt)?.label}
                </span>
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 