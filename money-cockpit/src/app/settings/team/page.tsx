'use client'

import { useState, useEffect, useCallback } from 'react'
import { Trash2, UserPlus, Clock, X, Lock } from 'lucide-react'
import { createBrowserClient } from '@/lib/supabase-client'
import { useWorkspace } from '@/lib/contexts/WorkspaceContext'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'

interface WorkspaceMember {
  user_id: string
  role: 'admin' | 'viewer'
  created_at: string
  profiles?: {
    email: string
  }
}

interface WorkspaceInvitation {
  id: string
  email: string
  role: 'admin' | 'viewer'
  created_at: string
  expires_at: string
  invited_by: string
  profiles?: {
    email: string
  }
}

export default function TeamPage() {
  const [members, setMembers] = useState<WorkspaceMember[]>([])
  const [invitations, setInvitations] = useState<WorkspaceInvitation[]>([])
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false)
  const [inviteEmail, setInviteEmail] = useState('')
  const [inviteRole, setInviteRole] = useState<'admin' | 'viewer'>('viewer')
  const [inviteWorkspaceId, setInviteWorkspaceId] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isInviting, setIsInviting] = useState(false)
  const [availableWorkspaces, setAvailableWorkspaces] = useState<any[]>([])
  const { currentWorkspaceId, currentWorkspace, isAdmin } = useWorkspace()
  const supabase = createBrowserClient()
  
  // Check if current workspace is private
  // Temporarily disable private workspace check to allow team invitations
  const isPrivateWorkspace = false // currentWorkspace ? (currentWorkspace as any)?.is_private === true : false

  // Debug logging
  console.log('Current workspace:', currentWorkspace)
  console.log('Is private workspace:', isPrivateWorkspace)
  console.log('Current workspace is_private value:', (currentWorkspace as any)?.is_private)
  console.log('Current workspace ID:', currentWorkspaceId)

  const fetchMembers = useCallback(async () => {
    // Use fallback workspace ID if currentWorkspaceId is not available
    const workspaceId = currentWorkspaceId || '982f9092-6b9e-463d-9cea-dc3572022f94'

    try {
      const response = await fetch(`/api/team/members?workspace_id=${workspaceId}`)
      const result = await response.json()

      if (!response.ok) {
        console.error('Error fetching members:', result.error)
        return
      }

      setMembers(result.members || [])
    } catch (error) {
      console.error('Error fetching members:', error)
    }
  }, [currentWorkspaceId])

  const fetchInvitations = useCallback(async () => {
    // Use fallback workspace ID if currentWorkspaceId is not available
    const workspaceId = currentWorkspaceId || '982f9092-6b9e-463d-9cea-dc3572022f94'

    try {
      const response = await fetch(`/api/team/invitations?workspace_id=${workspaceId}`)
      const result = await response.json()

      if (!response.ok) {
        console.error('Error fetching invitations:', result.error)
        return
      }

      setInvitations(result.invitations || [])
    } catch (error) {
      console.error('Error fetching invitations:', error)
    }
  }, [currentWorkspaceId])

  const fetchAvailableWorkspaces = useCallback(async () => {
    try {
      const { data: workspacesData, error } = await supabase.rpc('get_user_workspaces')

      if (error) {
        console.error('Error fetching available workspaces:', error)
        return
      }

      // Filter to only show workspaces where user is admin
      const adminWorkspaces = workspacesData?.filter((w: any) => w.user_role === 'admin') || []
      setAvailableWorkspaces(adminWorkspaces)

      // Set default workspace for invitation
      if (adminWorkspaces.length > 0 && !inviteWorkspaceId) {
        const defaultWorkspace = adminWorkspaces.find((w: any) => w.workspace_id === currentWorkspaceId) || adminWorkspaces[0]
        setInviteWorkspaceId(defaultWorkspace.workspace_id)
      }
    } catch (error) {
      console.error('Error fetching available workspaces:', error)
    }
  }, [supabase, currentWorkspaceId, inviteWorkspaceId])

  const handleInvite = async () => {
    if (!inviteEmail || !inviteWorkspaceId) {
      alert('Please enter an email address and select a workspace.')
      return
    }

    setIsInviting(true)
    try {
      const response = await fetch('/api/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: inviteEmail,
          role: inviteRole,
          workspace_id: inviteWorkspaceId
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to invite user')
      }

      const selectedWorkspace = availableWorkspaces.find(w => w.workspace_id === inviteWorkspaceId)
      const workspaceName = selectedWorkspace?.workspace_name || 'the workspace'

      alert(`Invitation sent successfully to ${inviteEmail} for ${workspaceName}!`)

      setInviteEmail('')
      setInviteRole('viewer')
      setIsInviteModalOpen(false)
      // Keep the selected workspace for next invitation
      await fetchInvitations()
    } catch (error) {
      console.error('Error inviting user:', error)
      alert(error instanceof Error ? error.message : 'Failed to invite user')
    } finally {
      setIsInviting(false)
    }
  }

  const handleCancelInvitation = async (invitationId: string) => {
    if (!confirm('Are you sure you want to cancel this invitation?')) return

    try {
      const response = await fetch(`/api/team/invitations?invitation_id=${invitationId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!response.ok) {
        console.error('Error canceling invitation:', result.error)
        alert('Failed to cancel invitation')
        return
      }

      await fetchInvitations()
    } catch (error) {
      console.error('Error canceling invitation:', error)
      alert('Failed to cancel invitation')
    }
  }

  const handleRemoveMember = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this member?')) return

    // Use fallback workspace ID if currentWorkspaceId is not available
    const workspaceId = currentWorkspaceId || '982f9092-6b9e-463d-9cea-dc3572022f94'

    try {
      const response = await fetch(`/api/team/members/manage?workspace_id=${workspaceId}&user_id=${userId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!response.ok) {
        console.error('Error removing member:', result.error)
        alert('Failed to remove member')
        return
      }

      await fetchMembers()
    } catch (error) {
      console.error('Error removing member:', error)
      alert('Failed to remove member')
    }
  }

  const handleRoleChange = async (userId: string, newRole: 'admin' | 'viewer') => {
    // Use fallback workspace ID if currentWorkspaceId is not available
    const workspaceId = currentWorkspaceId || '982f9092-6b9e-463d-9cea-dc3572022f94'

    try {
      const response = await fetch('/api/team/members/manage', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspace_id: workspaceId,
          user_id: userId,
          role: newRole
        })
      })

      const result = await response.json()

      if (!response.ok) {
        console.error('Error updating role:', result.error)
        alert('Failed to update role')
        return
      }

      await fetchMembers()
    } catch (error) {
      console.error('Error updating role:', error)
      alert('Failed to update role')
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      await Promise.all([fetchMembers(), fetchInvitations(), fetchAvailableWorkspaces()])
      setIsLoading(false)
    }
    loadData()
  }, [currentWorkspaceId, fetchMembers, fetchInvitations, fetchAvailableWorkspaces])

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Show message for private workspaces
  if (isPrivateWorkspace) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Team Members</h1>
        </div>
        
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
          <div className="flex items-center gap-3">
            <Lock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">
                Private Workspace
              </h3>
              <p className="text-yellow-700 dark:text-yellow-300 mt-1">
                This is a private workspace. Team collaboration features are only available in shared workspaces.
              </p>
              <p className="text-sm text-yellow-600 dark:text-yellow-400 mt-2">
                To collaborate with others, create a new shared workspace from your profile settings.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Team Members</h1>
        {isAdmin && (
          <button
            onClick={() => {
              // Set default workspace when opening modal
              if (availableWorkspaces.length > 0 && !inviteWorkspaceId) {
                const defaultWorkspace = availableWorkspaces.find((w: any) => w.workspace_id === currentWorkspaceId) || availableWorkspaces[0]
                setInviteWorkspaceId(defaultWorkspace.workspace_id)
              }
              setIsInviteModalOpen(true)
            }}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <UserPlus className="h-5 w-5" />
            Invite Member
          </button>
        )}
      </div>

      {/* Active Members */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden mb-6">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Active Members</h2>
        </div>
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Email
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Role
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Joined
              </th>
              {isAdmin && (
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {members.map((member) => (
              <tr key={member.user_id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {member.profiles?.email || member.user_id}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {isAdmin ? (
                    <select
                      value={member.role}
                      onChange={(e) => handleRoleChange(member.user_id, e.target.value as 'admin' | 'viewer')}
                      className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="admin">Admin</option>
                      <option value="viewer">Viewer</option>
                    </select>
                  ) : (
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      member.role === 'admin' 
                        ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {member.role}
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {new Date(member.created_at).toLocaleDateString()}
                </td>
                {isAdmin && (
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleRemoveMember(member.user_id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pending Invitations */}
      {isAdmin && invitations.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Pending Invitations</h2>
          </div>
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Invited By
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Expires
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {invitations.map((invitation) => (
                <tr key={invitation.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {invitation.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      invitation.role === 'admin' 
                        ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {invitation.role}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {invitation.profiles?.email || 'Unknown'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {new Date(invitation.expires_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleCancelInvitation(invitation.id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      title="Cancel invitation"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Invite Modal */}
      <Transition appear show={isInviteModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={setIsInviteModalOpen}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 dark:text-white"
                  >
                    Invite Team Member
                  </Dialog.Title>
                  <div className="mt-4 space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Workspace {availableWorkspaces.length > 0 && (
                          <span className="text-xs text-gray-500">({availableWorkspaces.length} available)</span>
                        )}
                      </label>
                      <select
                        value={inviteWorkspaceId}
                        onChange={(e) => setInviteWorkspaceId(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="">Select a workspace...</option>
                        {availableWorkspaces.map((workspace) => (
                          <option key={workspace.workspace_id} value={workspace.workspace_id}>
                            {workspace.workspace_name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={inviteEmail}
                        onChange={(e) => setInviteEmail(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Enter email address"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Role
                      </label>
                      <select
                        value={inviteRole}
                        onChange={(e) => setInviteRole(e.target.value as 'admin' | 'viewer')}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="viewer">Viewer</option>
                        <option value="admin">Admin</option>
                      </select>
                    </div>
                  </div>

                  <div className="mt-6 flex gap-3">
                    <button
                      type="button"
                      className="flex-1 inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      onClick={() => setIsInviteModalOpen(false)}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      disabled={!inviteEmail || !inviteWorkspaceId || isInviting}
                      className="flex-1 inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={handleInvite}
                    >
                      {isInviting ? 'Inviting...' : 'Send Invite'}
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  )
}