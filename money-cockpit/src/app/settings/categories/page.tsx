'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { Plus, Edit2, Archive, Check, X } from 'lucide-react'
import AddCategoryModal from '@/components/modals/AddCategoryModal'

interface Category {
  id: string
  name: string
  scope: 'personal' | 'business' | 'both'
  gst_default: boolean
  is_active: boolean
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editForm, setEditForm] = useState<Partial<Category>>({})
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  
  const supabase = createBrowserClient()

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    setIsLoading(true)
    
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (!error && data) {
      setCategories(data)
    }
    
    setIsLoading(false)
  }

  const handleEdit = (category: Category) => {
    setEditingId(category.id)
    setEditForm({
      name: category.name,
      scope: category.scope,
      gst_default: category.gst_default
    })
  }

  const handleSave = async () => {
    if (!editingId) return

    const { error } = await supabase
      .from('categories')
      .update(editForm)
      .eq('id', editingId)

    if (!error) {
      setCategories(categories.map(c => 
        c.id === editingId ? { ...c, ...editForm } : c
      ))
      setEditingId(null)
      setEditForm({})
    }
  }

  const handleArchive = async (categoryId: string) => {
    if (!confirm('Archive this category? It will no longer appear in dropdowns.')) return

    const { error } = await supabase
      .from('categories')
      .update({ is_active: false })
      .eq('id', categoryId)

    if (!error) {
      setCategories(categories.filter(c => c.id !== categoryId))
    }
  }

  const getScopeColor = (scope: string) => {
    switch (scope) {
      case 'personal': return 'text-green-400'
      case 'business': return 'text-blue-400'
      case 'both': return 'text-purple-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Categories</h1>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus className="h-5 w-5" />
          New Category
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="bg-black border-2 border-cyan-500 overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="border-b border-cyan-500">
                <th className="px-4 py-3 text-left text-xs font-mono text-cyan-500">NAME</th>
                <th className="px-4 py-3 text-left text-xs font-mono text-cyan-500">SCOPE</th>
                <th className="px-4 py-3 text-center text-xs font-mono text-cyan-500">GST DEFAULT</th>
                <th className="px-4 py-3 text-right text-xs font-mono text-cyan-500">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {categories.length === 0 ? (
                <tr>
                  <td colSpan={4} className="px-4 py-8 text-center text-gray-500 font-mono text-sm">
                    No categories yet. Create your first one!
                  </td>
                </tr>
              ) : (
                categories.map((category) => (
                  <tr key={category.id} className="border-b border-gray-700 hover:bg-cyan-500/10">
                    <td className="px-4 py-3">
                      {editingId === category.id ? (
                        <input
                          type="text"
                          value={editForm.name || ''}
                          onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                          className="bg-gray-900 border border-cyan-500 text-white font-mono px-2 py-1 text-sm"
                          autoFocus
                        />
                      ) : (
                        <span className="text-sm font-mono text-white">{category.name}</span>
                      )}
                    </td>
                    <td className="px-4 py-3">
                      {editingId === category.id ? (
                        <select
                          value={editForm.scope || ''}
                          onChange={(e) => setEditForm({ ...editForm, scope: e.target.value as 'personal' | 'business' | 'both' })}
                          className="bg-gray-900 border border-cyan-500 text-white font-mono px-2 py-1 text-sm"
                        >
                          <option value="personal">PERSONAL</option>
                          <option value="business">BUSINESS</option>
                          <option value="both">BOTH</option>
                        </select>
                      ) : (
                        <span className={`text-sm font-mono ${getScopeColor(category.scope)}`}>
                          {category.scope.toUpperCase()}
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {editingId === category.id ? (
                        <input
                          type="checkbox"
                          checked={editForm.gst_default || false}
                          onChange={(e) => setEditForm({ ...editForm, gst_default: e.target.checked })}
                          className="w-4 h-4"
                        />
                      ) : (
                        <span className="text-sm font-mono text-gray-400">
                          {category.gst_default ? '✓' : '—'}
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3">
                      {editingId === category.id ? (
                        <div className="flex items-center justify-end gap-2">
                          <button
                            onClick={handleSave}
                            className="text-green-500 hover:text-green-400"
                          >
                            <Check className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => {
                              setEditingId(null)
                              setEditForm({})
                            }}
                            className="text-gray-500 hover:text-gray-400"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ) : (
                        <div className="flex items-center justify-end gap-2">
                          <button
                            onClick={() => handleEdit(category)}
                            className="text-gray-500 hover:text-white"
                          >
                            <Edit2 className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleArchive(category.id)}
                            className="text-red-500 hover:text-red-400"
                          >
                            <Archive className="h-4 w-4" />
                          </button>
                        </div>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* CSV Import Stub */}
      <div className="mt-6 flex justify-end">
        <button
          onClick={() => alert('CSV import coming soon!')}
          className="text-sm text-gray-500 hover:text-gray-400 font-mono"
        >
          Import CSV →
        </button>
      </div>

      {/* Add Category Modal */}
      <AddCategoryModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={() => {
          fetchCategories()
          setIsAddModalOpen(false)
        }}
      />
    </div>
  )
} 