'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { useWorkspace } from '@/lib/contexts/WorkspaceContext'
import { Entity } from '@/types/database'
import { Plus, Edit2, Power, Trash2 } from 'lucide-react'
import EntityModal from '@/components/settings/EntityModal'

export default function BusinessProfilesPage() {
  const [entities, setEntities] = useState<Entity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingEntity, setEditingEntity] = useState<Entity | null>(null)
  
  const supabase = createBrowserClient()

  useEffect(() => {
    fetchEntities()
  }, [])

  const fetchEntities = async () => {
    setIsLoading(true)
    const { data, error } = await supabase
      .from('entities')
      .select('*')
      .order('type')
      .order('name')

    if (!error && data) {
      setEntities(data)
    }
    setIsLoading(false)
  }

  const handleEdit = (entity: Entity) => {
    setEditingEntity(entity)
    setShowModal(true)
  }

  const handleAdd = () => {
    setEditingEntity(null)
    setShowModal(true)
  }

  const handleToggleActive = async (entity: Entity) => {
    const { error } = await supabase
      .from('entities')
      .update({ is_active: !entity.is_active })
      .eq('id', entity.id)

    if (!error) {
      fetchEntities()
    } else {
      console.error('Error toggling entity status:', error)
      alert('Failed to update entity status')
    }
  }

  const handleDelete = async (entity: Entity) => {
    if (!confirm(`Are you sure you want to delete ${entity.name}? This action cannot be undone.`)) {
      return
    }

    const { error } = await supabase
      .from('entities')
      .delete()
      .eq('id', entity.id)

    if (!error) {
      fetchEntities()
    } else {
      console.error('Error deleting entity:', error)
      alert('Failed to delete entity. It may have associated data.')
    }
  }

  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'personal':
        return '👤'
      case 'business':
        return '🏢'
      case 'trust':
        return '🏛️'
      default:
        return '📁'
    }
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Business Profiles
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Manage your personal, business, and trust entities
            </p>
          </div>
          <button
            onClick={handleAdd}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Plus className="h-5 w-5" />
            New Business
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  ABN/ACN
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Currency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  GST
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {entities.map((entity) => (
                <tr key={entity.id} className={!entity.is_active ? 'opacity-50' : ''}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-2xl mr-3">{getEntityIcon(entity.type)}</span>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {entity.name}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                      {entity.type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {entity.abn_acn || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {entity.default_currency}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {entity.is_gst ? '✅' : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      entity.is_active 
                        ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-400'
                    }`}>
                      {entity.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleEdit(entity)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                    >
                      <Edit2 className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleToggleActive(entity)}
                      className={`mr-3 ${
                        entity.is_active 
                          ? 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300'
                          : 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300'
                      }`}
                    >
                      <Power className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(entity)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {showModal && (
        <EntityModal
          entity={editingEntity}
          onClose={() => {
            setShowModal(false)
            setEditingEntity(null)
          }}
          onSuccess={() => {
            setShowModal(false)
            setEditingEntity(null)
            fetchEntities()
          }}
        />
      )}
    </div>
  )
} 