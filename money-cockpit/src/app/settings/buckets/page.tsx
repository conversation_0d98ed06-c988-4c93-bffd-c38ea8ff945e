'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'
import { useWorkspace } from '@/lib/contexts/WorkspaceContext'
import { Package } from 'lucide-react'

interface BucketData {
  name: string
  target_pct: number
  color: string
}

export default function BucketsPage() {
  const [buckets, setBuckets] = useState<BucketData[]>([
    { name: 'Daily', target_pct: 60, color: 'text-green-400' },
    { name: 'Splurge', target_pct: 10, color: 'text-pink-400' },
    { name: 'Smile', target_pct: 10, color: 'text-yellow-400' },
    { name: 'Fire', target_pct: 20, color: 'text-red-400' }
  ])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  
  const supabase = createBrowserClient()
  const { currentWorkspaceId } = useWorkspace()

  useEffect(() => {
    fetchBuckets()
  }, [currentWorkspaceId])

  const fetchBuckets = async () => {
    if (!currentWorkspaceId) return
    
    setIsLoading(true)
    
    try {
      // Fetch buckets for the current workspace
      const { data, error } = await supabase
        .from('buckets')
        .select('name, target_pct')
        .eq('workspace_id', currentWorkspaceId)
        .order('name')

      if (!error && data) {
        // If no buckets exist, create defaults
        if (data.length === 0) {
          const defaultBuckets = [
            { workspace_id: currentWorkspaceId, name: 'Daily', target_pct: 60 },
            { workspace_id: currentWorkspaceId, name: 'Splurge', target_pct: 10 },
            { workspace_id: currentWorkspaceId, name: 'Smile', target_pct: 10 },
            { workspace_id: currentWorkspaceId, name: 'Fire', target_pct: 20 }
          ]
          
          const { error: insertError } = await supabase
            .from('buckets')
            .insert(defaultBuckets)
          
          if (!insertError) {
            // Fetch again after creating defaults
            const { data: newData } = await supabase
              .from('buckets')
              .select('name, target_pct')
              .eq('workspace_id', currentWorkspaceId)
              .order('name')
            
            if (newData) {
              setBuckets(prevBuckets => 
                prevBuckets.map(bucket => {
                  const dbBucket = newData.find(b => b.name === bucket.name)
                  return dbBucket ? { ...bucket, target_pct: dbBucket.target_pct } : bucket
                })
              )
            }
          }
        } else {
          setBuckets(prevBuckets => 
            prevBuckets.map(bucket => {
              const dbBucket = data.find(b => b.name === bucket.name)
              return dbBucket ? { ...bucket, target_pct: dbBucket.target_pct } : bucket
            })
          )
        }
      }
    } catch (error) {
      console.error('Error fetching buckets:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handlePercentageChange = (bucketName: string, value: string) => {
    const numValue = parseInt(value) || 0
    if (numValue < 0 || numValue > 100) return

    setBuckets(buckets.map(bucket => 
      bucket.name === bucketName ? { ...bucket, target_pct: numValue } : bucket
    ))
  }

  const getTotalPercentage = () => {
    return buckets.reduce((sum, bucket) => sum + bucket.target_pct, 0)
  }

  const handleSave = async () => {
    if (getTotalPercentage() !== 100 || !currentWorkspaceId) return

    setIsSaving(true)
    
    try {
      // Update each bucket
      for (const bucket of buckets) {
        const { error } = await supabase
          .from('buckets')
          .update({ target_pct: bucket.target_pct })
          .eq('workspace_id', currentWorkspaceId)
          .eq('name', bucket.name)

        if (error) throw error
      }

      // Show success feedback
      alert('Buckets updated successfully!')
    } catch (error) {
      console.error('Error saving buckets:', error)
      alert('Failed to save buckets')
    } finally {
      setIsSaving(false)
    }
  }

  const total = getTotalPercentage()
  const isValid = total === 100

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
          <Package className="h-6 w-6" />
          Barefoot Buckets
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Configure how your income is distributed across buckets. Must total 100%.
        </p>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="max-w-2xl">
          <div className="bg-black border-2 border-cyan-500 p-6">
            {/* Bucket Inputs */}
            <div className="grid grid-cols-2 gap-6">
              {buckets.map((bucket) => (
                <div key={bucket.name} className="space-y-2">
                  <label className={`font-mono text-sm ${bucket.color}`}>
                    {bucket.name.toUpperCase()} BUCKET
                  </label>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={bucket.target_pct}
                      onChange={(e) => handlePercentageChange(bucket.name, e.target.value)}
                      className="w-24 bg-gray-900 border-2 border-gray-600 text-white font-mono px-3 py-2 text-center focus:border-cyan-500 focus:outline-none"
                    />
                    <span className="text-gray-400 font-mono">%</span>
                  </div>
                </div>
              ))}
            </div>

            {/* Total Display */}
            <div className="mt-8 pt-6 border-t-2 border-gray-700">
              <div className="flex items-center justify-between">
                <span className="font-mono text-cyan-500">TOTAL</span>
                <span className={`font-mono text-2xl ${
                  isValid ? 'text-green-400' : 'text-red-400'
                }`}>
                  {total}%
                </span>
              </div>
              {!isValid && (
                <p className="text-red-400 font-mono text-xs mt-2">
                  Total must equal 100% (currently {total > 100 ? 'over' : 'under'} by {Math.abs(100 - total)}%)
                </p>
              )}
            </div>

            {/* Save Button */}
            <div className="mt-6">
              <button
                onClick={handleSave}
                disabled={!isValid || isSaving}
                className={`
                  w-full border-2 font-mono py-3 transition-all
                  ${!isValid || isSaving
                    ? 'bg-gray-800 border-gray-600 text-gray-500 cursor-not-allowed'
                    : 'bg-cyan-500 border-cyan-400 text-black hover:scale-105'
                  }
                `}
              >
                {isSaving ? 'SAVING...' : 'SAVE BUCKETS'}
              </button>
            </div>

            {/* Visual Representation */}
            <div className="mt-8">
              <div className="h-8 flex overflow-hidden border-2 border-gray-600">
                {buckets.map((bucket, index) => (
                  <div
                    key={bucket.name}
                    style={{ width: `${bucket.target_pct}%` }}
                    className={`${
                      index === 0 ? 'bg-green-500' :
                      index === 1 ? 'bg-pink-500' :
                      index === 2 ? 'bg-yellow-500' :
                      'bg-red-500'
                    } transition-all duration-300`}
                  />
                ))}
              </div>
            </div>

            {/* Pixel corners */}
            <div className="absolute top-0 left-0 w-2 h-2 bg-cyan-500" />
            <div className="absolute top-0 right-0 w-2 h-2 bg-cyan-500" />
            <div className="absolute bottom-0 left-0 w-2 h-2 bg-cyan-500" />
            <div className="absolute bottom-0 right-0 w-2 h-2 bg-cyan-500" />
          </div>

          {/* Info Box */}
          <div className="mt-6 bg-gray-900 border-2 border-gray-700 p-4">
            <p className="text-gray-400 font-mono text-xs leading-relaxed">
              The Barefoot Investor bucket system helps you automatically distribute your income:
              <br />• DAILY: Everyday expenses (60%)
              <br />• SPLURGE: Guilt-free spending (10%)
              <br />• SMILE: Short-term savings (10%)
              <br />• FIRE: Financial Independence (20%)
            </p>
          </div>
        </div>
      )}
    </div>
  )
} 