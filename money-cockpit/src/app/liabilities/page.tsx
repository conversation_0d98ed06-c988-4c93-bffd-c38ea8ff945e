'use client'

import { Plus } from 'lucide-react'
import { useState } from 'react'
import DebtModal from '@/components/modals/DebtModal'
import AppLayout from '@/components/layouts/AppLayout'

export default function LiabilitiesPage() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <AppLayout title="Debts & Liabilities">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Debts & Liabilities</h1>
          <button
            onClick={() => setIsModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            <Plus className="h-5 w-5" />
            Add Debt
          </button>
        </div>

        <div className="bg-black border-2 border-red-500 p-8">
          <p className="text-center text-gray-500 font-mono">
            Debts and liabilities list coming soon...
          </p>
        </div>

        <DebtModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSuccess={() => setIsModalOpen(false)}
        />
      </div>
    </AppLayout>
  )
} 