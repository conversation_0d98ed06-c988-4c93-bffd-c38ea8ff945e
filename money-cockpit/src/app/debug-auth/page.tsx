'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'

export default function DebugAuthPage() {
  const [authStatus, setAuthStatus] = useState<any>(null)
  const [workspaceTest, setWorkspaceTest] = useState<any>(null)
  const [inviteTest, setInviteTest] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createBrowserClient()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      // Test authentication
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      setAuthStatus({
        user: user ? { id: user.id, email: user.email } : null,
        userError: userError?.message,
        session: session ? 'exists' : 'null',
        sessionError: sessionError?.message
      })

      if (user) {
        // Test workspace creation API
        try {
          const workspaceResponse = await fetch('/api/test-workspace-creation')
          const workspaceResult = await workspaceResponse.json()
          setWorkspaceTest({
            status: workspaceResponse.status,
            result: workspaceResult
          })
        } catch (error) {
          setWorkspaceTest({
            status: 'error',
            result: { error: 'Failed to test workspace creation', details: error }
          })
        }

        // Test get_user_workspaces function
        try {
          const { data: workspaces, error } = await supabase.rpc('get_user_workspaces')
          console.log('User workspaces:', workspaces)
          console.log('Workspaces error:', error)
        } catch (error) {
          console.error('Error fetching workspaces:', error)
        }

        // Test debug auth API
        try {
          const debugResponse = await fetch('/api/debug-auth')
          const debugResult = await debugResponse.json()
          setInviteTest({
            status: debugResponse.status,
            result: debugResult
          })
        } catch (error) {
          setInviteTest({
            status: 'error',
            result: { error: 'Failed to test debug auth', details: error }
          })
        }
      }
    } catch (error) {
      setAuthStatus({
        error: 'Failed to check auth',
        details: error
      })
    } finally {
      setLoading(false)
    }
  }

  const testWorkspaceCreation = async () => {
    try {
      const response = await fetch('/api/workspace', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Test Shared Workspace ' + Date.now(),
          is_private: false
        })
      })
      
      const result = await response.json()
      alert(`Workspace creation test: ${response.status} - ${JSON.stringify(result)}`)
    } catch (error) {
      alert(`Error: ${error}`)
    }
  }

  const testInvitation = async () => {
    const email = prompt('Enter email to invite:')
    if (!email) return

    try {
      const response = await fetch('/api/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          role: 'viewer',
          workspace_id: 'cd8d82fd-22c3-4cdf-843f-fce416770014' // Default workspace ID
        })
      })
      
      const result = await response.json()
      alert(`Invitation test: ${response.status} - ${JSON.stringify(result)}`)
    } catch (error) {
      alert(`Error: ${error}`)
    }
  }

  if (loading) {
    return <div className="p-6">Loading authentication debug info...</div>
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug Page</h1>
      
      <div className="space-y-6">
        {/* Authentication Status */}
        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(authStatus, null, 2)}
          </pre>
        </div>

        {/* Workspace Test */}
        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Workspace Creation Test</h2>
          <pre className="text-sm overflow-auto mb-4">
            {JSON.stringify(workspaceTest, null, 2)}
          </pre>
          <button
            onClick={testWorkspaceCreation}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Test Workspace Creation
          </button>
        </div>

        {/* Invite Test */}
        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Debug Auth API Test</h2>
          <pre className="text-sm overflow-auto mb-4">
            {JSON.stringify(inviteTest, null, 2)}
          </pre>
          <button
            onClick={testInvitation}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Test Team Invitation
          </button>
        </div>

        {/* Manual Tests */}
        <div className="bg-yellow-100 dark:bg-yellow-900 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Manual Test Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>First, run the SQL script in <code>fix_workspace_authentication_issues.sql</code> in your Supabase dashboard</li>
            <li>Refresh this page to see updated test results</li>
            <li>Try creating a shared workspace using the "Test Workspace Creation" button</li>
            <li>Try inviting a team member using the "Test Team Invitation" button</li>
            <li>Check the browser console and network tab for any errors</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
