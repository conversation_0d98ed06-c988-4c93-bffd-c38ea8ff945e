'use client'

import { Plus } from 'lucide-react'
import { useState } from 'react'
import IncomeStreamModal from '@/components/modals/IncomeStreamModal'
import AppLayout from '@/components/layouts/AppLayout'

export default function IncomePage() {
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <AppLayout title="Income Streams">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Income Streams</h1>
          <button
            onClick={() => setIsModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            <Plus className="h-5 w-5" />
            Add Income Stream
          </button>
        </div>

        <div className="bg-black border-2 border-green-500 p-8">
          <p className="text-center text-gray-500 font-mono">
            Income streams list coming soon...
          </p>
        </div>

        <IncomeStreamModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSuccess={() => setIsModalOpen(false)}
        />
      </div>
    </AppLayout>
  )
} 