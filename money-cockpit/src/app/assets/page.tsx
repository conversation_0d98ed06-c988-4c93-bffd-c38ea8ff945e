'use client'

import { Plus } from 'lucide-react'
import AppLayout from '@/components/layouts/AppLayout'

export default function AssetsPage() {
  return (
    <AppLayout title="Assets">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Assets</h1>
          <button
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Plus className="h-5 w-5" />
            Add Asset
          </button>
        </div>

        <div className="bg-black border-2 border-blue-500 p-8">
          <p className="text-center text-gray-500 font-mono">
            Assets tracking coming soon...
          </p>
        </div>
      </div>
    </AppLayout>
  )
} 