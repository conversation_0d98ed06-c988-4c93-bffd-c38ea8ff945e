# Source Code Structure

This directory contains all the source code for the MoneyMofos application. The structure follows Next.js 15 App Router conventions with additional organization for maintainability.

## 📁 Directory Overview

### `/app` - Next.js App Router
Contains all pages, layouts, and API routes following the App Router file-based routing system.

- **Pages**: Each folder represents a route in the application
- **Layouts**: Shared layouts for different sections
- **API Routes**: Server-side API endpoints
- **Middleware**: Request/response processing

### `/components` - Reusable React Components
Organized by functionality and reusability level.

- **UI Components**: Base design system components
- **Feature Components**: Business logic components
- **Layout Components**: Page structure components
- **Modal Components**: Dialog and overlay components

### `/lib` - Utility Libraries
Shared utilities, configurations, and helper functions.

- **Contexts**: React Context providers for global state
- **Hooks**: Custom React hooks for reusable logic
- **Utils**: Helper functions and utilities
- **Configurations**: App-wide configuration files

### `/types` - TypeScript Definitions
Centralized type definitions for the entire application.

- **Database Types**: Supabase-generated types
- **Component Types**: Props and state interfaces
- **API Types**: Request/response type definitions
- **Utility Types**: Helper and generic types

## 🏗️ Architecture Principles

### Component Organization
```
components/
├── ui/           # Base components (Button, Input, etc.)
├── forms/        # Form-specific components
├── modals/       # Dialog and modal components
├── layouts/      # Page layout components
├── dashboard/    # Dashboard-specific components
└── settings/     # Settings page components
```

### State Management
- **React Context**: Global state (workspace, user preferences)
- **SWR**: Server state and caching
- **React Hook Form**: Form state management
- **Local State**: Component-specific state with useState

### Data Flow
1. **API Layer**: Supabase client for database operations
2. **Custom Hooks**: Encapsulate data fetching logic
3. **Components**: Consume data through hooks
4. **Context**: Share global state across components

### File Naming Conventions
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Hooks**: camelCase with "use" prefix (e.g., `useUserData.ts`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Types**: PascalCase (e.g., `UserTypes.ts`)

## 🔧 Development Guidelines

### Component Structure
```typescript
// Component template
'use client' // If client-side rendering needed

import { useState, useEffect } from 'react'
import { ComponentProps } from '@/types'

interface Props {
  // Define props with TypeScript
}

export default function ComponentName({ prop1, prop2 }: Props) {
  // Component logic
  
  return (
    // JSX structure
  )
}
```

### Custom Hooks Pattern
```typescript
// Custom hook template
import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'

export function useCustomHook() {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Hook logic

  return { data, loading, error }
}
```

### Error Handling
- Use try-catch blocks for async operations
- Provide user-friendly error messages
- Log errors for debugging
- Implement fallback UI states

### Performance Considerations
- Use React.memo for expensive components
- Implement lazy loading for routes
- Optimize bundle size with tree shaking
- Use SWR for efficient data fetching

## 📚 Key Concepts

### Workspace System
The application uses a multi-tenant workspace system where:
- Users can belong to multiple workspaces
- Each workspace has its own data isolation
- Role-based permissions control access levels

### ADHD-Friendly Design
- Visual feedback for all actions
- Clear navigation patterns
- Minimal cognitive load
- Achievement system for engagement

### Real-time Updates
- Supabase real-time subscriptions
- Optimistic UI updates
- Conflict resolution strategies
- Offline-first approach where possible

## 🔍 Code Quality

### TypeScript Usage
- Strict mode enabled
- No `any` types allowed
- Proper interface definitions
- Generic types where appropriate

### Testing Strategy
- Unit tests for utilities
- Component testing with React Testing Library
- Integration tests for critical flows
- E2E tests for user journeys

### Accessibility
- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility

---

For specific implementation details, check the README files in each subdirectory. 