import useSWR from 'swr'
import { createBrowserClient } from '@/lib/supabase-client'

interface Category {
  id: string
  name: string
  scope: string
  gst_default: boolean
  is_active: boolean
}

const fetcher = async (scope?: string) => {
  const supabase = createBrowserClient()
  let query = supabase
    .from('categories')
    .select('*')
    .eq('is_active', true)
    .order('name')
  
  if (scope && scope !== 'both') {
    query = query.or(`scope.eq.${scope},scope.eq.both`)
  }
  
  const { data, error } = await query
  
  if (error) throw error
  return data as Category[]
}

export function useCategories(scope?: 'personal' | 'business' | 'both') {
  const { data: categories = [], error, mutate } = useSWR(
    scope ? ['categories', scope] : 'categories', 
    () => fetcher(scope)
  )
  
  return {
    categories,
    isLoading: !error && !categories,
    isError: error,
    mutate
  }
} 