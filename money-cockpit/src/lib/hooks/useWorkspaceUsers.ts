import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'

interface WorkspaceUser {
  user_id: string
  email: string
  display_name?: string
}

export function useWorkspaceUsers() {
  const [users, setUsers] = useState<WorkspaceUser[]>([])
  const [loading, setLoading] = useState(true)
  
  const supabase = createBrowserClient()

  useEffect(() => {
    const fetchWorkspaceUsers = async () => {
      try {
        setLoading(true)

        // Get current user for now
        const { data: { user: currentUser } } = await supabase.auth.getUser()

        if (!currentUser) {
          setUsers([])
          return
        }

        // For now, just return current user
        const formattedUsers: WorkspaceUser[] = [{
          user_id: currentUser.id,
          email: currentUser.email || 'Unknown',
          display_name: currentUser.email?.split('@')[0] || 'Me'
        }]

        setUsers(formattedUsers)
      } catch (err) {
        console.error('Error fetching workspace users:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchWorkspaceUsers()
  }, [supabase])

  return { users, loading }
} 