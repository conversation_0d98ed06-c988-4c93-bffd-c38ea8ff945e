'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

type Theme = 'retro' | 'normal'

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: ReactNode }) {
  const [theme, setTheme] = useState<Theme>('retro')

  useEffect(() => {
    // Load theme from localStorage
    const savedTheme = localStorage.getItem('theme') as Theme
    if (savedTheme && (savedTheme === 'retro' || savedTheme === 'normal')) {
      setTheme(savedTheme)
    }
  }, [])

  useEffect(() => {
    // Apply theme classes to document
    if (theme === 'retro') {
      document.documentElement.classList.add('retro-theme')
      document.documentElement.classList.remove('normal-theme')
    } else {
      document.documentElement.classList.add('normal-theme')
      document.documentElement.classList.remove('retro-theme')
    }
    
    // Save to localStorage
    localStorage.setItem('theme', theme)
  }, [theme])

  const toggleTheme = () => {
    setTheme(prev => prev === 'retro' ? 'normal' : 'retro')
  }

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
} 