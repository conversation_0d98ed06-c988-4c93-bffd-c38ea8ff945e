# Lib Directory - Utilities & Shared Code

This directory contains utility libraries, configurations, and shared code that supports the entire application. It's organized into logical modules for maintainability and reusability.

## 📁 Directory Structure

### `/contexts` - React Context Providers
Global state management using React Context:
- **WorkspaceContext**: Multi-tenant workspace management
- **ThemeContext**: Dark/light mode theming
- **UserContext**: User authentication and preferences
- **NotificationContext**: Toast notifications and alerts

### `/hooks` - Custom React Hooks
Reusable logic encapsulated in custom hooks:
- **useCategories**: Category data management
- **useWorkspace**: Workspace operations
- **useAuth**: Authentication utilities
- **useLocalStorage**: Persistent local storage
- **useDebounce**: Input debouncing

### `/utils` - Utility Functions
Helper functions and utilities:
- **formatCurrency**: Currency formatting
- **dateUtils**: Date manipulation and formatting
- **validation**: Form validation helpers
- **api**: API client utilities
- **constants**: Application constants

### `/types` - Shared Type Definitions
TypeScript interfaces and types:
- **database**: Supabase generated types
- **api**: API request/response types
- **components**: Shared component prop types
- **utils**: Utility function types

## 🏗️ Architecture Patterns

### Context Pattern
```typescript
// Context definition
interface WorkspaceContextType {
  currentWorkspace: Workspace | null
  workspaces: Workspace[]
  switchWorkspace: (workspaceId: string) => void
  loading: boolean
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined)

// Provider component
export function WorkspaceProvider({ children }: { children: React.ReactNode }) {
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null)
  // ... context logic
  
  return (
    <WorkspaceContext.Provider value={contextValue}>
      {children}
    </WorkspaceContext.Provider>
  )
}

// Hook for consuming context
export function useWorkspace() {
  const context = useContext(WorkspaceContext)
  if (!context) {
    throw new Error('useWorkspace must be used within WorkspaceProvider')
  }
  return context
}
```

### Custom Hook Pattern
```typescript
// Data fetching hook
export function useCategories() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchCategories() {
      try {
        setLoading(true)
        const data = await api.getCategories()
        setCategories(data)
      } catch (err) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchCategories()
  }, [])

  return { categories, loading, error, refetch: fetchCategories }
}
```

### Utility Function Pattern
```typescript
// Pure utility function with TypeScript
export function formatCurrency(
  amount: number,
  currency: string = 'AUD',
  locale: string = 'en-AU'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

// Usage
const formatted = formatCurrency(1234.56) // "$1,234.56"
```

## 🔧 Core Utilities

### Supabase Client
```typescript
// lib/supabase-client.ts
import { createBrowserClient } from '@supabase/ssr'

export function createBrowserClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```

### API Client
```typescript
// lib/api.ts
class ApiClient {
  private supabase = createBrowserClient()

  async getCategories(): Promise<Category[]> {
    const { data, error } = await this.supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) throw new Error(error.message)
    return data
  }

  async createTransaction(transaction: CreateTransactionData): Promise<Transaction> {
    const { data, error } = await this.supabase
      .from('transactions')
      .insert(transaction)
      .select()
      .single()

    if (error) throw new Error(error.message)
    return data
  }
}

export const api = new ApiClient()
```

### Validation Utilities
```typescript
// lib/validation.ts
import { z } from 'zod'

export const transactionSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  description: z.string().min(1, 'Description is required'),
  category_id: z.string().uuid('Invalid category'),
  entity_id: z.string().uuid('Invalid entity'),
})

export function validateTransaction(data: unknown) {
  return transactionSchema.safeParse(data)
}
```

## 🎯 Context Providers

### Workspace Context
Manages multi-tenant workspace functionality:
```typescript
export function useWorkspace() {
  const context = useContext(WorkspaceContext)
  
  return {
    currentWorkspace: context.currentWorkspace,
    workspaces: context.workspaces,
    switchWorkspace: context.switchWorkspace,
    createWorkspace: context.createWorkspace,
    inviteUser: context.inviteUser,
    loading: context.loading,
  }
}
```

### Theme Context
Handles dark/light mode theming:
```typescript
export function useTheme() {
  const context = useContext(ThemeContext)
  
  return {
    theme: context.theme, // 'light' | 'dark' | 'system'
    setTheme: context.setTheme,
    isDark: context.isDark,
    toggleTheme: context.toggleTheme,
  }
}
```

### Notification Context
Manages toast notifications:
```typescript
export function useNotifications() {
  const context = useContext(NotificationContext)
  
  return {
    showSuccess: context.showSuccess,
    showError: context.showError,
    showInfo: context.showInfo,
    showWarning: context.showWarning,
    dismiss: context.dismiss,
  }
}
```

## 🪝 Custom Hooks

### Data Fetching Hooks
```typescript
// useEntities.ts
export function useEntities() {
  const { data, error, mutate } = useSWR('/api/entities', api.getEntities)
  
  return {
    entities: data || [],
    loading: !error && !data,
    error,
    refresh: mutate,
  }
}

// useTransactions.ts
export function useTransactions(filters?: TransactionFilters) {
  const key = filters ? ['/api/transactions', filters] : '/api/transactions'
  const { data, error, mutate } = useSWR(key, () => api.getTransactions(filters))
  
  return {
    transactions: data || [],
    loading: !error && !data,
    error,
    refresh: mutate,
  }
}
```

### Form Hooks
```typescript
// useFormPersistence.ts
export function useFormPersistence<T>(key: string, defaultValues: T) {
  const [values, setValues] = useState<T>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(key)
      return saved ? JSON.parse(saved) : defaultValues
    }
    return defaultValues
  })

  useEffect(() => {
    localStorage.setItem(key, JSON.stringify(values))
  }, [key, values])

  return [values, setValues] as const
}
```

### Utility Hooks
```typescript
// useDebounce.ts
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// useLocalStorage.ts
export function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      return initialValue
    }
  })

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      window.localStorage.setItem(key, JSON.stringify(valueToStore))
    } catch (error) {
      console.error('Error saving to localStorage:', error)
    }
  }

  return [storedValue, setValue] as const
}
```

## 🛠️ Utility Functions

### Date Utilities
```typescript
// lib/utils/dateUtils.ts
export function formatDate(date: Date | string, format: 'short' | 'long' = 'short'): string {
  const d = new Date(date)
  
  if (format === 'short') {
    return d.toLocaleDateString('en-AU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }
  
  return d.toLocaleDateString('en-AU', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function isToday(date: Date | string): boolean {
  const today = new Date()
  const compareDate = new Date(date)
  
  return today.toDateString() === compareDate.toDateString()
}

export function addDays(date: Date, days: number): Date {
  const result = new Date(date)
  result.setDate(result.getDate() + days)
  return result
}
```

### Currency Utilities
```typescript
// lib/utils/currency.ts
export function formatCurrency(amount: number, currency = 'AUD'): string {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency,
  }).format(amount)
}

export function parseCurrency(value: string): number {
  return parseFloat(value.replace(/[^0-9.-]+/g, ''))
}

export function calculateGST(amount: number, rate = 0.1): number {
  return amount * rate
}
```

### Validation Utilities
```typescript
// lib/utils/validation.ts
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidABN(abn: string): boolean {
  const abnRegex = /^\d{11}$/
  return abnRegex.test(abn.replace(/\s/g, ''))
}

export function validateRequired(value: any, fieldName: string): string | null {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return `${fieldName} is required`
  }
  return null
}
```

## 📊 Performance Considerations

### Memoization
```typescript
// Memoize expensive calculations
export const calculateBudgetStatus = useMemo(() => {
  return (budget: Budget, transactions: Transaction[]) => {
    // Expensive calculation
    return result
  }
}, [])
```

### Lazy Loading
```typescript
// Lazy load heavy utilities
const heavyUtil = lazy(() => import('./heavyUtility'))
```

### Bundle Optimization
- Tree shaking for unused utilities
- Separate chunks for heavy libraries
- Dynamic imports for optional features

## 🧪 Testing Utilities

### Test Helpers
```typescript
// lib/test-utils.ts
export function createMockWorkspace(overrides?: Partial<Workspace>): Workspace {
  return {
    id: 'test-workspace',
    name: 'Test Workspace',
    created_at: new Date().toISOString(),
    ...overrides,
  }
}

export function renderWithProviders(ui: React.ReactElement) {
  return render(
    <WorkspaceProvider>
      <ThemeProvider>
        {ui}
      </ThemeProvider>
    </WorkspaceProvider>
  )
}
```

## 🔐 Security Utilities

### Input Sanitization
```typescript
// lib/utils/security.ts
export function sanitizeInput(input: string): string {
  return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
}

export function validateCSRF(token: string): boolean {
  // CSRF token validation logic
  return true
}
```

---

This library structure provides a solid foundation for scalable React application development with proper separation of concerns and reusable utilities. 