// Retro Gaming Theme Configuration

export const retroTheme = {
  colors: {
    // Classic arcade colors
    neonGreen: '#00FF41',
    retroPink: '#FF006E',
    arcadeBlue: '#0080FF',
    pixelPurple: '#8B00FF',
    coinGold: '#FFD700',
    laserRed: '#FF0040',
    
    // Game state colors
    health: {
      full: '#00FF00',
      medium: '#FFFF00',
      low: '#FF4500',
      critical: '#FF0000'
    },
    
    // UI colors
    background: '#0A0A0A',
    surface: '#1A1A1A',
    border: '#333333',
    text: {
      primary: '#FFFFFF',
      secondary: '#B0B0B0',
      accent: '#00FF41'
    }
  },
  
  fonts: {
    pixel: '"Press Start 2P", monospace',
    mono: '"Courier New", monospace',
    system: 'system-ui, -apple-system, sans-serif'
  },
  
  sounds: {
    coinCollect: '/sounds/coin.mp3',
    levelUp: '/sounds/levelup.mp3',
    achievement: '/sounds/achievement.mp3',
    gameOver: '/sounds/gameover.mp3'
  },
  
  animations: {
    pixelBounce: 'pixelBounce 0.5s steps(4) infinite',
    glitch: 'glitch 0.3s steps(2) infinite',
    blink: 'blink 1s steps(2) infinite',
    scanline: 'scanline 8s linear infinite'
  }
}

// Achievement definitions
export const achievements = {
  firstTransaction: {
    id: 'first_transaction',
    name: 'First Blood',
    description: 'Record your first transaction',
    icon: '🎮',
    points: 100
  },
  debtDestroyer: {
    id: 'debt_destroyer',
    name: 'Debt Destroyer',
    description: 'Pay off a debt completely',
    icon: '💥',
    points: 500
  },
  savingsStreak: {
    id: 'savings_streak',
    name: 'Savings Streak',
    description: 'Save money for 7 days straight',
    icon: '🔥',
    points: 300
  },
  budgetBoss: {
    id: 'budget_boss',
    name: 'Budget Boss',
    description: 'Stay under budget for a month',
    icon: '👑',
    points: 1000
  },
  invoiceHunter: {
    id: 'invoice_hunter',
    name: 'Invoice Hunter',
    description: 'Get paid on 5 invoices',
    icon: '🎯',
    points: 400
  }
}

// Game-style level system based on net worth
export const getLevelFromNetWorth = (netWorth: number) => {
  const levels = [
    { level: 1, minWorth: -Infinity, title: 'Space Cadet', color: '#808080' },
    { level: 2, minWorth: 0, title: 'Rookie Pilot', color: '#00FF41' },
    { level: 3, minWorth: 1000, title: 'Star Fighter', color: '#0080FF' },
    { level: 4, minWorth: 5000, title: 'Squadron Leader', color: '#8B00FF' },
    { level: 5, minWorth: 10000, title: 'Wing Commander', color: '#FFD700' },
    { level: 6, minWorth: 25000, title: 'Space Admiral', color: '#FF006E' },
    { level: 7, minWorth: 50000, title: 'Galactic Tycoon', color: '#FF0040' },
    { level: 8, minWorth: 100000, title: 'Universe Master', color: '#00FFFF' }
  ]
  
  return levels.reverse().find(l => netWorth >= l.minWorth) || levels[0]
}

// Pixel art style CSS animations
export const pixelAnimations = `
  @keyframes pixelBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-4px); }
  }
  
  @keyframes glitch {
    0% { transform: translate(0); }
    20% { transform: translate(-1px, 1px); }
    40% { transform: translate(-1px, -1px); }
    60% { transform: translate(1px, 1px); }
    80% { transform: translate(1px, -1px); }
    100% { transform: translate(0); }
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  @keyframes scanline {
    0% { transform: translateY(-100%); }
    100% { transform: translateY(100%); }
  }
  
  .pixel-corners {
    clip-path: polygon(
      0 8px, 8px 8px, 8px 0,
      calc(100% - 8px) 0, calc(100% - 8px) 8px, 100% 8px,
      100% calc(100% - 8px), calc(100% - 8px) calc(100% - 8px), calc(100% - 8px) 100%,
      8px 100%, 8px calc(100% - 8px), 0 calc(100% - 8px)
    );
  }
  
  .retro-text {
    text-shadow: 2px 2px 0px rgba(0, 255, 65, 0.5);
  }
  
  .arcade-glow {
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
  }
` 