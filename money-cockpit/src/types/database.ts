export interface Entity {
  id: string
  owner_id: string
  name: string
  type: 'personal' | 'business' | 'trust'
  abn_acn?: string
  default_currency: string
  is_gst: boolean
  is_active: boolean
  created_at: string
}

export interface Account {
  id: string
  entity_id: string
  provider: string
  currency: string
  display_name: string
  card_wallet_tag?: string
  api_key?: string
  created_at: string
}

export interface Transaction {
  id: string
  account_id: string
  amount: number
  currency: string
  date: string
  category?: string
  service_type?: string
  personal_category?: string
  bucket?: string
  memo?: string
  stream_id?: string
  created_at: string
}

export interface Debt {
  id: string
  entity_id: string
  creditor: string
  original_amount: number
  balance: number
  interest_rate: number
  compounding: string
  payment_freq: string
  next_due: string
  created_at: string
}

export interface RecurringExpense {
  id: string
  entity_id: string
  name: string
  exp_type: string
  frequency: string
  amount: number
  currency: string
  next_due: string
  created_at: string
}

export interface IncomeStream {
  id: string
  entity_id: string
  name: string
  counterparty: string
  stream_type: string
  ownership_pct: number
  currency: string
  data_source?: string
  start_date?: string
  end_date?: string
  created_at: string
}

export interface Goal {
  id: string
  owner_id: string
  entity_id?: string
  name: string
  target_amount?: number
  target_date?: string
  status: string
  icon?: string
  created_at: string
}

export interface Invoice {
  id: string
  entity_id: string
  stripe_id?: string
  client: string
  amount: number
  tax_amount: number
  currency: string
  status: string
  due_date: string
  paid_date?: string
  pdf_url?: string
  created_at: string
}

export interface TaxCalendar {
  id: string
  entity_id: string
  tax_type: string
  due_date: string
  est_amount?: number
  status: string
  created_at: string
}

export interface Bucket {
  name: string
  target_pct: number
}

export interface CategoryRule {
  id: number
  category: string
  match_regex: string
  created_at: string
}

export interface AgentInsight {
  id: string
  lens: string
  headline: string
  body: string
  severity: 'info' | 'warning' | 'success'
  created_at: string
}

export interface DashboardData {
  net_worth: number
  total_assets: number
  total_debt: number
  upcoming_payments: number
  lens: string
}

export interface DebtPayoffForecast {
  avalanche: Array<{
    creditor: string
    balance: number
    interest_rate: number
    entity: string
  }>
  snowball: Array<{
    creditor: string
    balance: number
    interest_rate: number
    entity: string
  }>
}

export interface Category {
  id: string
  name: string
  scope: 'personal' | 'business' | 'both'
  gst_default: boolean
  created_at: string
}

export interface UserPrefs {
  id: string
  user_id: string
  pay_day: string
  date_fmt: string
  default_lens: string
  last_viewed_lens?: string
  created_at: string
  updated_at: string
}

export interface Budget {
  id: string
  user_id: string
  entity_id: string | null
  scope: 'personal' | 'business'
  bucket: string | null
  category_id: string | null
  period: 'weekly' | 'monthly'
  amount: number
  currency: string
  created_at: string
  updated_at: string
}

export interface BudgetSnapshot {
  id: string
  snapshot_date: string
  user_id: string
  entity_id: string | null
  bucket: string | null
  category_id: string | null
  spent: number
  budget: number
  currency: string
  created_at: string
}

export interface Achievement {
  id: string
  user_id: string
  achievement_id: string
  unlocked_at: string
  points: number
  created_at: string
}

export interface UserStats {
  user_id: string
  total_points: number
  current_level: number
  transactions_count: number
  debts_paid_count: number
  savings_streak_days: number
  last_transaction_date: string | null
  created_at: string
  updated_at: string
} 