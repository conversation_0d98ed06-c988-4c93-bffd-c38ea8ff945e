-- Diagnostic script to check workspace setup
-- Run this in your Supabase SQL editor to see what's missing

-- 1. Check if workspace tables exist
SELECT 
  'workspaces' as table_name,
  EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'workspaces') as exists
UNION ALL
SELECT 
  'workspace_users' as table_name,
  EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'workspace_users') as exists
UNION ALL
SELECT 
  'workspace_invitations' as table_name,
  EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'workspace_invitations') as exists;

-- 2. Check if functions exist
SELECT 
  'get_user_workspaces' as function_name,
  EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'get_user_workspaces') as exists
UNION ALL
SELECT 
  'create_workspace' as function_name,
  EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'create_workspace') as exists
UNION ALL
SELECT 
  'is_workspace_admin' as function_name,
  EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'is_workspace_admin') as exists
UNION ALL
SELECT 
  'accept_workspace_invitation' as function_name,
  EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'accept_workspace_invitation') as exists;

-- 3. Check your current user
SELECT 
  id as user_id,
  email,
  created_at
FROM auth.users
WHERE id = auth.uid();

-- 4. Check if you have any workspaces
SELECT 
  w.id as workspace_id,
  w.name as workspace_name,
  wu.role,
  wu.created_at as joined_at
FROM workspace_users wu
JOIN workspaces w ON wu.workspace_id = w.id
WHERE wu.user_id = auth.uid();

-- 5. Check RLS policies on workspaces table
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE tablename = 'workspaces'
ORDER BY policyname; 