# MoneyMofos - ADHD-Friendly Finance Command Centre

A comprehensive multi-user finance management application built with Next.js 15, TypeScript, and Supabase. Designed specifically for ADHD-friendly financial management with household/team sharing capabilities.

## 🚀 Features

### Core Financial Management
- **Multi-Entity Support**: Manage personal and business finances separately
- **Real-time Dashboard**: Live financial overview with key metrics
- **Transaction Management**: Income, expenses, and categorization
- **Asset & Liability Tracking**: Complete financial position monitoring
- **Budget Management**: Create and track budgets with visual indicators
- **Invoice Management**: Create, upload, and track invoices
- **Recurring Expenses**: Automated recurring transaction handling
- **One-Off Expense Form**: Manual expense entry with auto-negation
- **Goals Module**: Financial goal tracking with progress visualization

### Multi-User Workspace System
- **Workspace Sharing**: Share financial data with household members or team
- **Role-Based Access**: Admin and viewer permissions
- **Email Invitations**: Invite users via email with automatic workspace assignment
- **Real-time Collaboration**: Live updates across all workspace members
- **Data Isolation**: Complete separation between different workspaces

### ADHD-Friendly Design
- **Visual Dashboard**: Clear, colorful visual indicators
- **Quick Actions**: Fast transaction entry and management
- **Achievement System**: Gamification to encourage engagement
- **Simplified Navigation**: Intuitive sidebar navigation
- **Mobile Responsive**: Works seamlessly on all devices

### One-Off Expense Management
- `/expenses` page records ad-hoc spends; auto-negates amount
- Floating "+ New Expense" button opens modal form
- Entity/Account selection with filtered categories by scope
- Bucket selection for personal entities only
- Real-time dashboard updates after expense recording

### Goals Module
- Add personal or business goals by target amount or date
- Progress bars visible under 'All' lens on dashboard
- Daily checker flips status to HIT and triggers celebration banner
- Support for Trust & Personal entities in goal creation
- Emoji icon selection for visual goal identification

### Owner Attribution (Personal Money)
- Accounts & one-off personal expenses can now be labelled Shared, Spence, Abi, etc.
- Dashboard filters & bucket bars show per-person splits
- Personal entity transactions support individual ownership tracking
- Shared expenses remain accessible to all workspace members
- Owner selection appears only for personal entities (not business/trust)

## 🏗️ Architecture

### Frontend
- **Next.js 15**: App Router with TypeScript
- **Tailwind CSS**: Utility-first styling
- **Headless UI**: Accessible component primitives
- **SWR**: Data fetching and caching
- **React Hook Form**: Form management and validation

### Backend
- **Supabase**: PostgreSQL database with real-time subscriptions
- **Row-Level Security**: Database-level access control
- **Edge Functions**: Serverless functions for business logic
- **Storage**: File uploads and management
- **Auth**: User authentication and session management

### Database Schema
- **Workspaces**: Multi-tenant workspace system
- **Entities**: Personal and business entity management
- **Transactions**: Financial transaction tracking
- **Budgets**: Budget creation and monitoring
- **Invoices**: Invoice management system
- **Users**: User management with workspace relationships

## 📁 Project Structure

```
money-cockpit/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (auth)/            # Authentication pages
│   │   ├── api/               # API routes
│   │   ├── dashboard/         # Main dashboard
│   │   ├── income/            # Income management
│   │   ├── expenses/          # One-off expense management
│   │   ├── recurring-expenses/ # Recurring expense management
│   │   ├── goals/             # Financial goal tracking
│   │   ├── assets/            # Asset tracking
│   │   ├── liabilities/       # Liability management
│   │   ├── invoices/          # Invoice management
│   │   └── settings/          # Application settings
│   ├── components/            # Reusable React components
│   │   ├── dashboard/         # Dashboard-specific components
│   │   ├── forms/             # Form components
│   │   ├── layouts/           # Layout components
│   │   ├── modals/            # Modal dialogs
│   │   ├── settings/          # Settings components
│   │   └── ui/                # Base UI components
│   ├── lib/                   # Utility libraries
│   │   ├── contexts/          # React contexts
│   │   └── hooks/             # Custom React hooks
│   └── types/                 # TypeScript type definitions
├── supabase/
│   ├── functions/             # Edge Functions
│   └── migrations/            # Database migrations
└── public/                    # Static assets
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/media-evolution/moneymofos.git
   cd moneymofos/money-cockpit
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Fill in your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

4. **Run database migrations**
   ```bash
   npx supabase db push
   ```

5. **Deploy edge functions**
   ```bash
   npx supabase functions deploy
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

7. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

### Code Quality
- **ESLint**: Enforced code standards
- **TypeScript**: Strict type checking
- **Prettier**: Code formatting (recommended)

## 🌐 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main

### Manual Deployment
1. Build the application: `npm run build`
2. Deploy the `.next` folder to your hosting provider
3. Ensure environment variables are set

## 🔐 Security

### Row-Level Security (RLS)
- All database tables have RLS policies
- Users can only access their workspace data
- Admins have write access, viewers have read-only access

### Authentication
- Supabase Auth handles user authentication
- JWT tokens for secure API access
- Email-based user invitations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript strict mode
- Use functional components with hooks
- Implement proper error handling
- Add JSDoc comments for functions
- Ensure mobile responsiveness

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or create an issue in the GitHub repository.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Powered by [Supabase](https://supabase.com/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Icons by [Lucide](https://lucide.dev/)

---

**MoneyMofos** - Making finance management accessible for everyone, especially those with ADHD. 💰🧠✨
