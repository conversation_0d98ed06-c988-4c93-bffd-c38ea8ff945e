-- Fix RLS recursion issue in workspace_users policies
-- Run this in your Supabase SQL Editor

-- Drop all workspace_users policies to fix recursion
DROP POLICY IF EXISTS "workspace_users_read" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_write" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_insert" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_update" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_admin_delete" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_insert" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_update" ON workspace_users;
DROP POLICY IF EXISTS "workspace_users_delete" ON workspace_users;

-- Create non-recursive policies for workspace_users
-- Allow users to read workspace_users for workspaces they belong to
CREATE POLICY "workspace_users_read" ON workspace_users
FOR SELECT
USING (user_id = auth.uid() OR workspace_id IN (
  SELECT workspace_id FROM workspace_users WHERE user_id = auth.uid()
));

-- Allow workspace admins to manage workspace users (non-recursive)
CREATE POLICY "workspace_users_admin_insert" ON workspace_users
FOR INSERT
WITH CHECK (
  workspace_id IN (
    SELECT workspace_id FROM workspace_users 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

CREATE POLICY "workspace_users_admin_update" ON workspace_users
FOR UPDATE
USING (
  workspace_id IN (
    SELECT workspace_id FROM workspace_users 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

CREATE POLICY "workspace_users_admin_delete" ON workspace_users
FOR DELETE
USING (
  workspace_id IN (
    SELECT workspace_id FROM workspace_users 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

-- Also update the specific private workspace to be shared
UPDATE workspaces 
SET is_private = false 
WHERE name = 'spencer''s Workspace';

-- Verify the changes
SELECT 
  id,
  name,
  is_private,
  created_at
FROM workspaces
ORDER BY created_at DESC;
