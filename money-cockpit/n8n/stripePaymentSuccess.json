{"name": "Stripe Payment Success", "nodes": [{"parameters": {"httpMethod": "POST", "path": "stripe-payment-webhook", "responseMode": "onReceived", "responseData": "allEntries", "responsePropertyName": "data"}, "id": "webhook", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 300], "webhookId": "stripe-payment-webhook"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"type\"]}}", "value2": "invoice.paid"}]}}, "id": "filter", "name": "Filter Invoice Paid", "type": "n8n-nodes-base.filter", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"operation": "update", "table": "invoices", "searchColumn": "stripe_id", "searchValue": "={{$json[\"data\"][\"object\"][\"id\"]}}", "updateColumns": "status,paid_date", "additionalFields": {"status": "PAID", "paid_date": "={{new Date().toISOString().split('T')[0]}}"}}, "id": "supabase1", "name": "Update Invoice Status", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [650, 300], "credentials": {"supabaseApi": {"id": "1", "name": "Supabase"}}}, {"parameters": {"values": {"string": [{"name": "stripe_id", "value": "={{$json[\"data\"][\"object\"][\"id\"]}}"}, {"name": "customer_name", "value": "={{$json[\"data\"][\"object\"][\"customer_name\"] || $json[\"data\"][\"object\"][\"customer_email\"]}}"}], "number": [{"name": "amount_paid", "value": "={{$json[\"data\"][\"object\"][\"amount_paid\"] / 100}}"}, {"name": "stripe_fee", "value": "={{($json[\"data\"][\"object\"][\"amount_paid\"] * 0.029 + 30) / 100}}"}, {"name": "net_amount", "value": "={{($json[\"data\"][\"object\"][\"amount_paid\"] - ($json[\"data\"][\"object\"][\"amount_paid\"] * 0.029 + 30)) / 100}}"}]}, "options": {}}, "id": "set", "name": "Calculate Amounts", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "splitBatch", "name": "Split Transactions", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"values": {"string": [{"name": "category", "value": "={{$json[\"transaction_type\"] === \"payment\" ? \"Revenue\" : \"Fees\"}}"}, {"name": "service_type", "value": "={{$json[\"transaction_type\"] === \"payment\" ? \"invoice_payment\" : \"payment_processing_fee\"}}"}, {"name": "memo", "value": "={{$json[\"transaction_type\"] === \"payment\" ? \"Payment for invoice \" + $node[\"set\"].json[\"stripe_id\"] + \" from \" + $node[\"set\"].json[\"customer_name\"] : \"Stripe processing fee for \" + $node[\"set\"].json[\"stripe_id\"]}}"}], "number": [{"name": "amount", "value": "={{$json[\"transaction_type\"] === \"payment\" ? $node[\"set\"].json[\"net_amount\"] : -$node[\"set\"].json[\"stripe_fee\"]}}"}], "dateTime": [{"name": "date", "value": "={{new Date().toISOString().split('T')[0]}}"}]}, "options": {}}, "id": "set2", "name": "Prepare Transaction Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"operation": "insert", "table": "transactions", "columns": "account_id,amount,currency,date,category,service_type,memo", "additionalFields": {}}, "id": "supabase2", "name": "Insert Transaction", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1450, 300], "credentials": {"supabaseApi": {"id": "1", "name": "Supabase"}}}, {"parameters": {"values": {"string": [{"name": "transaction_type", "value": "={{$runIndex === 0 ? \"payment\" : \"fee\"}}"}]}, "options": {}}, "id": "setTransactionType", "name": "Set Transaction Type", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1050, 500]}], "connections": {"webhook": {"main": [[{"node": "filter", "type": "main", "index": 0}]]}, "filter": {"main": [[{"node": "supabase1", "type": "main", "index": 0}]]}, "supabase1": {"main": [[{"node": "set", "type": "main", "index": 0}]]}, "set": {"main": [[{"node": "splitBatch", "type": "main", "index": 0}]]}, "splitBatch": {"main": [[{"node": "setTransactionType", "type": "main", "index": 0}]]}, "setTransactionType": {"main": [[{"node": "set2", "type": "main", "index": 0}]]}, "set2": {"main": [[{"node": "supabase2", "type": "main", "index": 0}]]}, "supabase2": {"main": [[{"node": "splitBatch", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "id": "stripe-payment-webhook"}