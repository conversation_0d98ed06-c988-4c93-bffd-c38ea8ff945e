{"name": "Stripe Invoice Webhook", "nodes": [{"parameters": {"httpMethod": "POST", "path": "stripe-invoice-webhook", "responseMode": "onReceived", "responseData": "allEntries", "responsePropertyName": "data"}, "id": "webhook", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 300], "webhookId": "stripe-invoice-webhook"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"type\"]}}", "value2": "invoice.finalized"}]}}, "id": "filter", "name": "Filter Invoice Finalized", "type": "n8n-nodes-base.filter", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"values": {"string": [{"name": "stripe_id", "value": "={{$json[\"data\"][\"object\"][\"id\"]}}"}, {"name": "client", "value": "={{$json[\"data\"][\"object\"][\"customer_name\"] || $json[\"data\"][\"object\"][\"customer_email\"]}}"}, {"name": "status", "value": "OPEN"}], "number": [{"name": "amount", "value": "={{$json[\"data\"][\"object\"][\"amount_due\"] / 100}}"}, {"name": "tax_amount", "value": "={{$json[\"data\"][\"object\"][\"tax\"] / 100}}"}], "dateTime": [{"name": "due_date", "value": "={{new Date($json[\"data\"][\"object\"][\"due_date\"] * 1000).toISOString().split('T')[0]}}"}]}, "options": {}}, "id": "set", "name": "Prepare Invoice Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"operation": "insert", "table": "invoices", "columns": "stripe_id,entity_id,client,amount,tax_amount,currency,status,due_date", "additionalFields": {}}, "id": "supabase1", "name": "Insert Invoice", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [850, 300], "credentials": {"supabaseApi": {"id": "1", "name": "Supabase"}}}, {"parameters": {"values": {"string": [{"name": "category", "value": "Accounts Receivable"}, {"name": "service_type", "value": "invoice_receivable"}, {"name": "memo", "value": "Invoice {{$node[\"set\"].json[\"stripe_id\"]}} - {{$node[\"set\"].json[\"client\"]}}"}], "number": [{"name": "amount", "value": "={{$node[\"set\"].json[\"amount\"]}}"}], "dateTime": [{"name": "date", "value": "={{new Date().toISOString().split('T')[0]}}"}]}, "options": {}}, "id": "set2", "name": "Prepare Transaction", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"operation": "insert", "table": "transactions", "columns": "account_id,amount,currency,date,category,service_type,memo", "additionalFields": {}}, "id": "supabase2", "name": "Insert Receivable Transaction", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1250, 300], "credentials": {"supabaseApi": {"id": "1", "name": "Supabase"}}}], "connections": {"webhook": {"main": [[{"node": "filter", "type": "main", "index": 0}]]}, "filter": {"main": [[{"node": "set", "type": "main", "index": 0}]]}, "set": {"main": [[{"node": "supabase1", "type": "main", "index": 0}]]}, "supabase1": {"main": [[{"node": "set2", "type": "main", "index": 0}]]}, "set2": {"main": [[{"node": "supabase2", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "id": "stripe-invoice-webhook"}